{"ast": null, "code": "var assocIndexOf = require('./_assocIndexOf');\n\n/** Used for built-in method references. */\nvar arrayProto = Array.prototype;\n\n/** Built-in value references. */\nvar splice = arrayProto.splice;\n\n/**\n * Removes `key` and its value from the list cache.\n *\n * @private\n * @name delete\n * @memberOf ListCache\n * @param {string} key The key of the value to remove.\n * @returns {boolean} Returns `true` if the entry was removed, else `false`.\n */\nfunction listCacheDelete(key) {\n  var data = this.__data__,\n    index = assocIndexOf(data, key);\n  if (index < 0) {\n    return false;\n  }\n  var lastIndex = data.length - 1;\n  if (index == lastIndex) {\n    data.pop();\n  } else {\n    splice.call(data, index, 1);\n  }\n  --this.size;\n  return true;\n}\nmodule.exports = listCacheDelete;", "map": {"version": 3, "names": ["assocIndexOf", "require", "arrayProto", "Array", "prototype", "splice", "listCacheDelete", "key", "data", "__data__", "index", "lastIndex", "length", "pop", "call", "size", "module", "exports"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/MVP/client/node_modules/lodash/_listCacheDelete.js"], "sourcesContent": ["var assocIndexOf = require('./_assocIndexOf');\n\n/** Used for built-in method references. */\nvar arrayProto = Array.prototype;\n\n/** Built-in value references. */\nvar splice = arrayProto.splice;\n\n/**\n * Removes `key` and its value from the list cache.\n *\n * @private\n * @name delete\n * @memberOf ListCache\n * @param {string} key The key of the value to remove.\n * @returns {boolean} Returns `true` if the entry was removed, else `false`.\n */\nfunction listCacheDelete(key) {\n  var data = this.__data__,\n      index = assocIndexOf(data, key);\n\n  if (index < 0) {\n    return false;\n  }\n  var lastIndex = data.length - 1;\n  if (index == lastIndex) {\n    data.pop();\n  } else {\n    splice.call(data, index, 1);\n  }\n  --this.size;\n  return true;\n}\n\nmodule.exports = listCacheDelete;\n"], "mappings": "AAAA,IAAIA,YAAY,GAAGC,OAAO,CAAC,iBAAiB,CAAC;;AAE7C;AACA,IAAIC,UAAU,GAAGC,KAAK,CAACC,SAAS;;AAEhC;AACA,IAAIC,MAAM,GAAGH,UAAU,CAACG,MAAM;;AAE9B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,eAAeA,CAACC,GAAG,EAAE;EAC5B,IAAIC,IAAI,GAAG,IAAI,CAACC,QAAQ;IACpBC,KAAK,GAAGV,YAAY,CAACQ,IAAI,EAAED,GAAG,CAAC;EAEnC,IAAIG,KAAK,GAAG,CAAC,EAAE;IACb,OAAO,KAAK;EACd;EACA,IAAIC,SAAS,GAAGH,IAAI,CAACI,MAAM,GAAG,CAAC;EAC/B,IAAIF,KAAK,IAAIC,SAAS,EAAE;IACtBH,IAAI,CAACK,GAAG,CAAC,CAAC;EACZ,CAAC,MAAM;IACLR,MAAM,CAACS,IAAI,CAACN,IAAI,EAAEE,KAAK,EAAE,CAAC,CAAC;EAC7B;EACA,EAAE,IAAI,CAACK,IAAI;EACX,OAAO,IAAI;AACb;AAEAC,MAAM,CAACC,OAAO,GAAGX,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}
{"ast": null, "code": "import max from \"./max.js\";\nimport maxIndex from \"./maxIndex.js\";\nimport min from \"./min.js\";\nimport minIndex from \"./minIndex.js\";\nimport quickselect from \"./quickselect.js\";\nimport number, { numbers } from \"./number.js\";\nimport { ascendingDefined } from \"./sort.js\";\nimport greatest from \"./greatest.js\";\nexport default function quantile(values, p, valueof) {\n  values = Float64Array.from(numbers(values, valueof));\n  if (!(n = values.length) || isNaN(p = +p)) return;\n  if (p <= 0 || n < 2) return min(values);\n  if (p >= 1) return max(values);\n  var n,\n    i = (n - 1) * p,\n    i0 = Math.floor(i),\n    value0 = max(quickselect(values, i0).subarray(0, i0 + 1)),\n    value1 = min(values.subarray(i0 + 1));\n  return value0 + (value1 - value0) * (i - i0);\n}\nexport function quantileSorted(values, p, valueof = number) {\n  if (!(n = values.length) || isNaN(p = +p)) return;\n  if (p <= 0 || n < 2) return +valueof(values[0], 0, values);\n  if (p >= 1) return +valueof(values[n - 1], n - 1, values);\n  var n,\n    i = (n - 1) * p,\n    i0 = Math.floor(i),\n    value0 = +valueof(values[i0], i0, values),\n    value1 = +valueof(values[i0 + 1], i0 + 1, values);\n  return value0 + (value1 - value0) * (i - i0);\n}\nexport function quantileIndex(values, p, valueof = number) {\n  if (isNaN(p = +p)) return;\n  numbers = Float64Array.from(values, (_, i) => number(valueof(values[i], i, values)));\n  if (p <= 0) return minIndex(numbers);\n  if (p >= 1) return maxIndex(numbers);\n  var numbers,\n    index = Uint32Array.from(values, (_, i) => i),\n    j = numbers.length - 1,\n    i = Math.floor(j * p);\n  quickselect(index, i, 0, j, (i, j) => ascendingDefined(numbers[i], numbers[j]));\n  i = greatest(index.subarray(0, i + 1), i => numbers[i]);\n  return i >= 0 ? i : -1;\n}", "map": {"version": 3, "names": ["max", "maxIndex", "min", "minIndex", "quickselect", "number", "numbers", "ascendingDefined", "greatest", "quantile", "values", "p", "valueof", "Float64Array", "from", "n", "length", "isNaN", "i", "i0", "Math", "floor", "value0", "subarray", "value1", "quantileSorted", "quantileIndex", "_", "index", "Uint32Array", "j"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/MVP/client/node_modules/d3-array/src/quantile.js"], "sourcesContent": ["import max from \"./max.js\";\nimport maxIndex from \"./maxIndex.js\";\nimport min from \"./min.js\";\nimport minIndex from \"./minIndex.js\";\nimport quickselect from \"./quickselect.js\";\nimport number, {numbers} from \"./number.js\";\nimport {ascendingDefined} from \"./sort.js\";\nimport greatest from \"./greatest.js\";\n\nexport default function quantile(values, p, valueof) {\n  values = Float64Array.from(numbers(values, valueof));\n  if (!(n = values.length) || isNaN(p = +p)) return;\n  if (p <= 0 || n < 2) return min(values);\n  if (p >= 1) return max(values);\n  var n,\n      i = (n - 1) * p,\n      i0 = Math.floor(i),\n      value0 = max(quickselect(values, i0).subarray(0, i0 + 1)),\n      value1 = min(values.subarray(i0 + 1));\n  return value0 + (value1 - value0) * (i - i0);\n}\n\nexport function quantileSorted(values, p, valueof = number) {\n  if (!(n = values.length) || isNaN(p = +p)) return;\n  if (p <= 0 || n < 2) return +valueof(values[0], 0, values);\n  if (p >= 1) return +valueof(values[n - 1], n - 1, values);\n  var n,\n      i = (n - 1) * p,\n      i0 = Math.floor(i),\n      value0 = +valueof(values[i0], i0, values),\n      value1 = +valueof(values[i0 + 1], i0 + 1, values);\n  return value0 + (value1 - value0) * (i - i0);\n}\n\nexport function quantileIndex(values, p, valueof = number) {\n  if (isNaN(p = +p)) return;\n  numbers = Float64Array.from(values, (_, i) => number(valueof(values[i], i, values)));\n  if (p <= 0) return minIndex(numbers);\n  if (p >= 1) return maxIndex(numbers);\n  var numbers,\n      index = Uint32Array.from(values, (_, i) => i),\n      j = numbers.length - 1,\n      i = Math.floor(j * p);\n  quickselect(index, i, 0, j, (i, j) => ascendingDefined(numbers[i], numbers[j]));\n  i = greatest(index.subarray(0, i + 1), (i) => numbers[i]);\n  return i >= 0 ? i : -1;\n}\n"], "mappings": "AAAA,OAAOA,GAAG,MAAM,UAAU;AAC1B,OAAOC,QAAQ,MAAM,eAAe;AACpC,OAAOC,GAAG,MAAM,UAAU;AAC1B,OAAOC,QAAQ,MAAM,eAAe;AACpC,OAAOC,WAAW,MAAM,kBAAkB;AAC1C,OAAOC,MAAM,IAAGC,OAAO,QAAO,aAAa;AAC3C,SAAQC,gBAAgB,QAAO,WAAW;AAC1C,OAAOC,QAAQ,MAAM,eAAe;AAEpC,eAAe,SAASC,QAAQA,CAACC,MAAM,EAAEC,CAAC,EAAEC,OAAO,EAAE;EACnDF,MAAM,GAAGG,YAAY,CAACC,IAAI,CAACR,OAAO,CAACI,MAAM,EAAEE,OAAO,CAAC,CAAC;EACpD,IAAI,EAAEG,CAAC,GAAGL,MAAM,CAACM,MAAM,CAAC,IAAIC,KAAK,CAACN,CAAC,GAAG,CAACA,CAAC,CAAC,EAAE;EAC3C,IAAIA,CAAC,IAAI,CAAC,IAAII,CAAC,GAAG,CAAC,EAAE,OAAOb,GAAG,CAACQ,MAAM,CAAC;EACvC,IAAIC,CAAC,IAAI,CAAC,EAAE,OAAOX,GAAG,CAACU,MAAM,CAAC;EAC9B,IAAIK,CAAC;IACDG,CAAC,GAAG,CAACH,CAAC,GAAG,CAAC,IAAIJ,CAAC;IACfQ,EAAE,GAAGC,IAAI,CAACC,KAAK,CAACH,CAAC,CAAC;IAClBI,MAAM,GAAGtB,GAAG,CAACI,WAAW,CAACM,MAAM,EAAES,EAAE,CAAC,CAACI,QAAQ,CAAC,CAAC,EAAEJ,EAAE,GAAG,CAAC,CAAC,CAAC;IACzDK,MAAM,GAAGtB,GAAG,CAACQ,MAAM,CAACa,QAAQ,CAACJ,EAAE,GAAG,CAAC,CAAC,CAAC;EACzC,OAAOG,MAAM,GAAG,CAACE,MAAM,GAAGF,MAAM,KAAKJ,CAAC,GAAGC,EAAE,CAAC;AAC9C;AAEA,OAAO,SAASM,cAAcA,CAACf,MAAM,EAAEC,CAAC,EAAEC,OAAO,GAAGP,MAAM,EAAE;EAC1D,IAAI,EAAEU,CAAC,GAAGL,MAAM,CAACM,MAAM,CAAC,IAAIC,KAAK,CAACN,CAAC,GAAG,CAACA,CAAC,CAAC,EAAE;EAC3C,IAAIA,CAAC,IAAI,CAAC,IAAII,CAAC,GAAG,CAAC,EAAE,OAAO,CAACH,OAAO,CAACF,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,EAAEA,MAAM,CAAC;EAC1D,IAAIC,CAAC,IAAI,CAAC,EAAE,OAAO,CAACC,OAAO,CAACF,MAAM,CAACK,CAAC,GAAG,CAAC,CAAC,EAAEA,CAAC,GAAG,CAAC,EAAEL,MAAM,CAAC;EACzD,IAAIK,CAAC;IACDG,CAAC,GAAG,CAACH,CAAC,GAAG,CAAC,IAAIJ,CAAC;IACfQ,EAAE,GAAGC,IAAI,CAACC,KAAK,CAACH,CAAC,CAAC;IAClBI,MAAM,GAAG,CAACV,OAAO,CAACF,MAAM,CAACS,EAAE,CAAC,EAAEA,EAAE,EAAET,MAAM,CAAC;IACzCc,MAAM,GAAG,CAACZ,OAAO,CAACF,MAAM,CAACS,EAAE,GAAG,CAAC,CAAC,EAAEA,EAAE,GAAG,CAAC,EAAET,MAAM,CAAC;EACrD,OAAOY,MAAM,GAAG,CAACE,MAAM,GAAGF,MAAM,KAAKJ,CAAC,GAAGC,EAAE,CAAC;AAC9C;AAEA,OAAO,SAASO,aAAaA,CAAChB,MAAM,EAAEC,CAAC,EAAEC,OAAO,GAAGP,MAAM,EAAE;EACzD,IAAIY,KAAK,CAACN,CAAC,GAAG,CAACA,CAAC,CAAC,EAAE;EACnBL,OAAO,GAAGO,YAAY,CAACC,IAAI,CAACJ,MAAM,EAAE,CAACiB,CAAC,EAAET,CAAC,KAAKb,MAAM,CAACO,OAAO,CAACF,MAAM,CAACQ,CAAC,CAAC,EAAEA,CAAC,EAAER,MAAM,CAAC,CAAC,CAAC;EACpF,IAAIC,CAAC,IAAI,CAAC,EAAE,OAAOR,QAAQ,CAACG,OAAO,CAAC;EACpC,IAAIK,CAAC,IAAI,CAAC,EAAE,OAAOV,QAAQ,CAACK,OAAO,CAAC;EACpC,IAAIA,OAAO;IACPsB,KAAK,GAAGC,WAAW,CAACf,IAAI,CAACJ,MAAM,EAAE,CAACiB,CAAC,EAAET,CAAC,KAAKA,CAAC,CAAC;IAC7CY,CAAC,GAAGxB,OAAO,CAACU,MAAM,GAAG,CAAC;IACtBE,CAAC,GAAGE,IAAI,CAACC,KAAK,CAACS,CAAC,GAAGnB,CAAC,CAAC;EACzBP,WAAW,CAACwB,KAAK,EAAEV,CAAC,EAAE,CAAC,EAAEY,CAAC,EAAE,CAACZ,CAAC,EAAEY,CAAC,KAAKvB,gBAAgB,CAACD,OAAO,CAACY,CAAC,CAAC,EAAEZ,OAAO,CAACwB,CAAC,CAAC,CAAC,CAAC;EAC/EZ,CAAC,GAAGV,QAAQ,CAACoB,KAAK,CAACL,QAAQ,CAAC,CAAC,EAAEL,CAAC,GAAG,CAAC,CAAC,EAAGA,CAAC,IAAKZ,OAAO,CAACY,CAAC,CAAC,CAAC;EACzD,OAAOA,CAAC,IAAI,CAAC,GAAGA,CAAC,GAAG,CAAC,CAAC;AACxB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "var baseGet = require('./_baseGet');\n\n/**\n * A specialized version of `baseProperty` which supports deep paths.\n *\n * @private\n * @param {Array|string} path The path of the property to get.\n * @returns {Function} Returns the new accessor function.\n */\nfunction basePropertyDeep(path) {\n  return function (object) {\n    return baseGet(object, path);\n  };\n}\nmodule.exports = basePropertyDeep;", "map": {"version": 3, "names": ["baseGet", "require", "basePropertyDeep", "path", "object", "module", "exports"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/MVP/client/node_modules/lodash/_basePropertyDeep.js"], "sourcesContent": ["var baseGet = require('./_baseGet');\n\n/**\n * A specialized version of `baseProperty` which supports deep paths.\n *\n * @private\n * @param {Array|string} path The path of the property to get.\n * @returns {Function} Returns the new accessor function.\n */\nfunction basePropertyDeep(path) {\n  return function(object) {\n    return baseGet(object, path);\n  };\n}\n\nmodule.exports = basePropertyDeep;\n"], "mappings": "AAAA,IAAIA,OAAO,GAAGC,OAAO,CAAC,YAAY,CAAC;;AAEnC;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,gBAAgBA,CAACC,IAAI,EAAE;EAC9B,OAAO,UAASC,MAAM,EAAE;IACtB,OAAOJ,OAAO,CAACI,MAAM,EAAED,IAAI,CAAC;EAC9B,CAAC;AACH;AAEAE,MAAM,CAACC,OAAO,GAAGJ,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}
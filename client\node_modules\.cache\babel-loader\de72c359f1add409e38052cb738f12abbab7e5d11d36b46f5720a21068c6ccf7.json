{"ast": null, "code": "var _excluded = [\"value\", \"background\"];\nvar _Bar;\nfunction _typeof(o) {\n  \"@babel/helpers - typeof\";\n\n  return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (o) {\n    return typeof o;\n  } : function (o) {\n    return o && \"function\" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? \"symbol\" : typeof o;\n  }, _typeof(o);\n}\nfunction _objectWithoutProperties(source, excluded) {\n  if (source == null) return {};\n  var target = _objectWithoutPropertiesLoose(source, excluded);\n  var key, i;\n  if (Object.getOwnPropertySymbols) {\n    var sourceSymbolKeys = Object.getOwnPropertySymbols(source);\n    for (i = 0; i < sourceSymbolKeys.length; i++) {\n      key = sourceSymbolKeys[i];\n      if (excluded.indexOf(key) >= 0) continue;\n      if (!Object.prototype.propertyIsEnumerable.call(source, key)) continue;\n      target[key] = source[key];\n    }\n  }\n  return target;\n}\nfunction _objectWithoutPropertiesLoose(source, excluded) {\n  if (source == null) return {};\n  var target = {};\n  for (var key in source) {\n    if (Object.prototype.hasOwnProperty.call(source, key)) {\n      if (excluded.indexOf(key) >= 0) continue;\n      target[key] = source[key];\n    }\n  }\n  return target;\n}\nfunction _extends() {\n  _extends = Object.assign ? Object.assign.bind() : function (target) {\n    for (var i = 1; i < arguments.length; i++) {\n      var source = arguments[i];\n      for (var key in source) {\n        if (Object.prototype.hasOwnProperty.call(source, key)) {\n          target[key] = source[key];\n        }\n      }\n    }\n    return target;\n  };\n  return _extends.apply(this, arguments);\n}\nfunction ownKeys(e, r) {\n  var t = Object.keys(e);\n  if (Object.getOwnPropertySymbols) {\n    var o = Object.getOwnPropertySymbols(e);\n    r && (o = o.filter(function (r) {\n      return Object.getOwnPropertyDescriptor(e, r).enumerable;\n    })), t.push.apply(t, o);\n  }\n  return t;\n}\nfunction _objectSpread(e) {\n  for (var r = 1; r < arguments.length; r++) {\n    var t = null != arguments[r] ? arguments[r] : {};\n    r % 2 ? ownKeys(Object(t), !0).forEach(function (r) {\n      _defineProperty(e, r, t[r]);\n    }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) {\n      Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r));\n    });\n  }\n  return e;\n}\nfunction _classCallCheck(instance, Constructor) {\n  if (!(instance instanceof Constructor)) {\n    throw new TypeError(\"Cannot call a class as a function\");\n  }\n}\nfunction _defineProperties(target, props) {\n  for (var i = 0; i < props.length; i++) {\n    var descriptor = props[i];\n    descriptor.enumerable = descriptor.enumerable || false;\n    descriptor.configurable = true;\n    if (\"value\" in descriptor) descriptor.writable = true;\n    Object.defineProperty(target, _toPropertyKey(descriptor.key), descriptor);\n  }\n}\nfunction _createClass(Constructor, protoProps, staticProps) {\n  if (protoProps) _defineProperties(Constructor.prototype, protoProps);\n  if (staticProps) _defineProperties(Constructor, staticProps);\n  Object.defineProperty(Constructor, \"prototype\", {\n    writable: false\n  });\n  return Constructor;\n}\nfunction _callSuper(t, o, e) {\n  return o = _getPrototypeOf(o), _possibleConstructorReturn(t, _isNativeReflectConstruct() ? Reflect.construct(o, e || [], _getPrototypeOf(t).constructor) : o.apply(t, e));\n}\nfunction _possibleConstructorReturn(self, call) {\n  if (call && (_typeof(call) === \"object\" || typeof call === \"function\")) {\n    return call;\n  } else if (call !== void 0) {\n    throw new TypeError(\"Derived constructors may only return object or undefined\");\n  }\n  return _assertThisInitialized(self);\n}\nfunction _assertThisInitialized(self) {\n  if (self === void 0) {\n    throw new ReferenceError(\"this hasn't been initialised - super() hasn't been called\");\n  }\n  return self;\n}\nfunction _isNativeReflectConstruct() {\n  try {\n    var t = !Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {}));\n  } catch (t) {}\n  return (_isNativeReflectConstruct = function _isNativeReflectConstruct() {\n    return !!t;\n  })();\n}\nfunction _getPrototypeOf(o) {\n  _getPrototypeOf = Object.setPrototypeOf ? Object.getPrototypeOf.bind() : function _getPrototypeOf(o) {\n    return o.__proto__ || Object.getPrototypeOf(o);\n  };\n  return _getPrototypeOf(o);\n}\nfunction _inherits(subClass, superClass) {\n  if (typeof superClass !== \"function\" && superClass !== null) {\n    throw new TypeError(\"Super expression must either be null or a function\");\n  }\n  subClass.prototype = Object.create(superClass && superClass.prototype, {\n    constructor: {\n      value: subClass,\n      writable: true,\n      configurable: true\n    }\n  });\n  Object.defineProperty(subClass, \"prototype\", {\n    writable: false\n  });\n  if (superClass) _setPrototypeOf(subClass, superClass);\n}\nfunction _setPrototypeOf(o, p) {\n  _setPrototypeOf = Object.setPrototypeOf ? Object.setPrototypeOf.bind() : function _setPrototypeOf(o, p) {\n    o.__proto__ = p;\n    return o;\n  };\n  return _setPrototypeOf(o, p);\n}\nfunction _defineProperty(obj, key, value) {\n  key = _toPropertyKey(key);\n  if (key in obj) {\n    Object.defineProperty(obj, key, {\n      value: value,\n      enumerable: true,\n      configurable: true,\n      writable: true\n    });\n  } else {\n    obj[key] = value;\n  }\n  return obj;\n}\nfunction _toPropertyKey(t) {\n  var i = _toPrimitive(t, \"string\");\n  return \"symbol\" == _typeof(i) ? i : i + \"\";\n}\nfunction _toPrimitive(t, r) {\n  if (\"object\" != _typeof(t) || !t) return t;\n  var e = t[Symbol.toPrimitive];\n  if (void 0 !== e) {\n    var i = e.call(t, r || \"default\");\n    if (\"object\" != _typeof(i)) return i;\n    throw new TypeError(\"@@toPrimitive must return a primitive value.\");\n  }\n  return (\"string\" === r ? String : Number)(t);\n}\n/**\n * @fileOverview Render a group of bar\n */\nimport React, { PureComponent } from 'react';\nimport clsx from 'clsx';\nimport Animate from 'react-smooth';\nimport isEqual from 'lodash/isEqual';\nimport isNil from 'lodash/isNil';\nimport { Layer } from '../container/Layer';\nimport { ErrorBar } from './ErrorBar';\nimport { Cell } from '../component/Cell';\nimport { LabelList } from '../component/LabelList';\nimport { uniqueId, mathSign, interpolateNumber } from '../util/DataUtils';\nimport { filterProps, findAllByType } from '../util/ReactUtils';\nimport { Global } from '../util/Global';\nimport { getCateCoordinateOfBar, getValueByDataKey, truncateByDomain, getBaseValueOfBar, findPositionOfBar, getTooltipItem } from '../util/ChartUtils';\nimport { adaptEventsOfChild } from '../util/types';\nimport { BarRectangle, minPointSizeCallback } from '../util/BarUtils';\nexport var Bar = /*#__PURE__*/function (_PureComponent) {\n  function Bar() {\n    var _this;\n    _classCallCheck(this, Bar);\n    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n      args[_key] = arguments[_key];\n    }\n    _this = _callSuper(this, Bar, [].concat(args));\n    _defineProperty(_this, \"state\", {\n      isAnimationFinished: false\n    });\n    _defineProperty(_this, \"id\", uniqueId('recharts-bar-'));\n    _defineProperty(_this, \"handleAnimationEnd\", function () {\n      var onAnimationEnd = _this.props.onAnimationEnd;\n      _this.setState({\n        isAnimationFinished: true\n      });\n      if (onAnimationEnd) {\n        onAnimationEnd();\n      }\n    });\n    _defineProperty(_this, \"handleAnimationStart\", function () {\n      var onAnimationStart = _this.props.onAnimationStart;\n      _this.setState({\n        isAnimationFinished: false\n      });\n      if (onAnimationStart) {\n        onAnimationStart();\n      }\n    });\n    return _this;\n  }\n  _inherits(Bar, _PureComponent);\n  return _createClass(Bar, [{\n    key: \"renderRectanglesStatically\",\n    value: function renderRectanglesStatically(data) {\n      var _this2 = this;\n      var _this$props = this.props,\n        shape = _this$props.shape,\n        dataKey = _this$props.dataKey,\n        activeIndex = _this$props.activeIndex,\n        activeBar = _this$props.activeBar;\n      var baseProps = filterProps(this.props, false);\n      return data && data.map(function (entry, i) {\n        var isActive = i === activeIndex;\n        var option = isActive ? activeBar : shape;\n        var props = _objectSpread(_objectSpread(_objectSpread({}, baseProps), entry), {}, {\n          isActive: isActive,\n          option: option,\n          index: i,\n          dataKey: dataKey,\n          onAnimationStart: _this2.handleAnimationStart,\n          onAnimationEnd: _this2.handleAnimationEnd\n        });\n        return /*#__PURE__*/React.createElement(Layer, _extends({\n          className: \"recharts-bar-rectangle\"\n        }, adaptEventsOfChild(_this2.props, entry, i), {\n          // https://github.com/recharts/recharts/issues/5415\n          // eslint-disable-next-line react/no-array-index-key\n          key: \"rectangle-\".concat(entry === null || entry === void 0 ? void 0 : entry.x, \"-\").concat(entry === null || entry === void 0 ? void 0 : entry.y, \"-\").concat(entry === null || entry === void 0 ? void 0 : entry.value, \"-\").concat(i)\n        }), /*#__PURE__*/React.createElement(BarRectangle, props));\n      });\n    }\n  }, {\n    key: \"renderRectanglesWithAnimation\",\n    value: function renderRectanglesWithAnimation() {\n      var _this3 = this;\n      var _this$props2 = this.props,\n        data = _this$props2.data,\n        layout = _this$props2.layout,\n        isAnimationActive = _this$props2.isAnimationActive,\n        animationBegin = _this$props2.animationBegin,\n        animationDuration = _this$props2.animationDuration,\n        animationEasing = _this$props2.animationEasing,\n        animationId = _this$props2.animationId;\n      var prevData = this.state.prevData;\n      return /*#__PURE__*/React.createElement(Animate, {\n        begin: animationBegin,\n        duration: animationDuration,\n        isActive: isAnimationActive,\n        easing: animationEasing,\n        from: {\n          t: 0\n        },\n        to: {\n          t: 1\n        },\n        key: \"bar-\".concat(animationId),\n        onAnimationEnd: this.handleAnimationEnd,\n        onAnimationStart: this.handleAnimationStart\n      }, function (_ref) {\n        var t = _ref.t;\n        var stepData = data.map(function (entry, index) {\n          var prev = prevData && prevData[index];\n          if (prev) {\n            var interpolatorX = interpolateNumber(prev.x, entry.x);\n            var interpolatorY = interpolateNumber(prev.y, entry.y);\n            var interpolatorWidth = interpolateNumber(prev.width, entry.width);\n            var interpolatorHeight = interpolateNumber(prev.height, entry.height);\n            return _objectSpread(_objectSpread({}, entry), {}, {\n              x: interpolatorX(t),\n              y: interpolatorY(t),\n              width: interpolatorWidth(t),\n              height: interpolatorHeight(t)\n            });\n          }\n          if (layout === 'horizontal') {\n            var _interpolatorHeight = interpolateNumber(0, entry.height);\n            var h = _interpolatorHeight(t);\n            return _objectSpread(_objectSpread({}, entry), {}, {\n              y: entry.y + entry.height - h,\n              height: h\n            });\n          }\n          var interpolator = interpolateNumber(0, entry.width);\n          var w = interpolator(t);\n          return _objectSpread(_objectSpread({}, entry), {}, {\n            width: w\n          });\n        });\n        return /*#__PURE__*/React.createElement(Layer, null, _this3.renderRectanglesStatically(stepData));\n      });\n    }\n  }, {\n    key: \"renderRectangles\",\n    value: function renderRectangles() {\n      var _this$props3 = this.props,\n        data = _this$props3.data,\n        isAnimationActive = _this$props3.isAnimationActive;\n      var prevData = this.state.prevData;\n      if (isAnimationActive && data && data.length && (!prevData || !isEqual(prevData, data))) {\n        return this.renderRectanglesWithAnimation();\n      }\n      return this.renderRectanglesStatically(data);\n    }\n  }, {\n    key: \"renderBackground\",\n    value: function renderBackground() {\n      var _this4 = this;\n      var _this$props4 = this.props,\n        data = _this$props4.data,\n        dataKey = _this$props4.dataKey,\n        activeIndex = _this$props4.activeIndex;\n      var backgroundProps = filterProps(this.props.background, false);\n      return data.map(function (entry, i) {\n        var value = entry.value,\n          background = entry.background,\n          rest = _objectWithoutProperties(entry, _excluded);\n        if (!background) {\n          return null;\n        }\n        var props = _objectSpread(_objectSpread(_objectSpread(_objectSpread(_objectSpread({}, rest), {}, {\n          fill: '#eee'\n        }, background), backgroundProps), adaptEventsOfChild(_this4.props, entry, i)), {}, {\n          onAnimationStart: _this4.handleAnimationStart,\n          onAnimationEnd: _this4.handleAnimationEnd,\n          dataKey: dataKey,\n          index: i,\n          className: 'recharts-bar-background-rectangle'\n        });\n        return /*#__PURE__*/React.createElement(BarRectangle, _extends({\n          key: \"background-bar-\".concat(i),\n          option: _this4.props.background,\n          isActive: i === activeIndex\n        }, props));\n      });\n    }\n  }, {\n    key: \"renderErrorBar\",\n    value: function renderErrorBar(needClip, clipPathId) {\n      if (this.props.isAnimationActive && !this.state.isAnimationFinished) {\n        return null;\n      }\n      var _this$props5 = this.props,\n        data = _this$props5.data,\n        xAxis = _this$props5.xAxis,\n        yAxis = _this$props5.yAxis,\n        layout = _this$props5.layout,\n        children = _this$props5.children;\n      var errorBarItems = findAllByType(children, ErrorBar);\n      if (!errorBarItems) {\n        return null;\n      }\n      var offset = layout === 'vertical' ? data[0].height / 2 : data[0].width / 2;\n      var dataPointFormatter = function dataPointFormatter(dataPoint, dataKey) {\n        /**\n         * if the value coming from `getComposedData` is an array then this is a stacked bar chart.\n         * arr[1] represents end value of the bar since the data is in the form of [startValue, endValue].\n         * */\n        var value = Array.isArray(dataPoint.value) ? dataPoint.value[1] : dataPoint.value;\n        return {\n          x: dataPoint.x,\n          y: dataPoint.y,\n          value: value,\n          errorVal: getValueByDataKey(dataPoint, dataKey)\n        };\n      };\n      var errorBarProps = {\n        clipPath: needClip ? \"url(#clipPath-\".concat(clipPathId, \")\") : null\n      };\n      return /*#__PURE__*/React.createElement(Layer, errorBarProps, errorBarItems.map(function (item) {\n        return /*#__PURE__*/React.cloneElement(item, {\n          key: \"error-bar-\".concat(clipPathId, \"-\").concat(item.props.dataKey),\n          data: data,\n          xAxis: xAxis,\n          yAxis: yAxis,\n          layout: layout,\n          offset: offset,\n          dataPointFormatter: dataPointFormatter\n        });\n      }));\n    }\n  }, {\n    key: \"render\",\n    value: function render() {\n      var _this$props6 = this.props,\n        hide = _this$props6.hide,\n        data = _this$props6.data,\n        className = _this$props6.className,\n        xAxis = _this$props6.xAxis,\n        yAxis = _this$props6.yAxis,\n        left = _this$props6.left,\n        top = _this$props6.top,\n        width = _this$props6.width,\n        height = _this$props6.height,\n        isAnimationActive = _this$props6.isAnimationActive,\n        background = _this$props6.background,\n        id = _this$props6.id;\n      if (hide || !data || !data.length) {\n        return null;\n      }\n      var isAnimationFinished = this.state.isAnimationFinished;\n      var layerClass = clsx('recharts-bar', className);\n      var needClipX = xAxis && xAxis.allowDataOverflow;\n      var needClipY = yAxis && yAxis.allowDataOverflow;\n      var needClip = needClipX || needClipY;\n      var clipPathId = isNil(id) ? this.id : id;\n      return /*#__PURE__*/React.createElement(Layer, {\n        className: layerClass\n      }, needClipX || needClipY ? /*#__PURE__*/React.createElement(\"defs\", null, /*#__PURE__*/React.createElement(\"clipPath\", {\n        id: \"clipPath-\".concat(clipPathId)\n      }, /*#__PURE__*/React.createElement(\"rect\", {\n        x: needClipX ? left : left - width / 2,\n        y: needClipY ? top : top - height / 2,\n        width: needClipX ? width : width * 2,\n        height: needClipY ? height : height * 2\n      }))) : null, /*#__PURE__*/React.createElement(Layer, {\n        className: \"recharts-bar-rectangles\",\n        clipPath: needClip ? \"url(#clipPath-\".concat(clipPathId, \")\") : null\n      }, background ? this.renderBackground() : null, this.renderRectangles()), this.renderErrorBar(needClip, clipPathId), (!isAnimationActive || isAnimationFinished) && LabelList.renderCallByParent(this.props, data));\n    }\n  }], [{\n    key: \"getDerivedStateFromProps\",\n    value: function getDerivedStateFromProps(nextProps, prevState) {\n      if (nextProps.animationId !== prevState.prevAnimationId) {\n        return {\n          prevAnimationId: nextProps.animationId,\n          curData: nextProps.data,\n          prevData: prevState.curData\n        };\n      }\n      if (nextProps.data !== prevState.curData) {\n        return {\n          curData: nextProps.data\n        };\n      }\n      return null;\n    }\n  }]);\n}(PureComponent);\n_Bar = Bar;\n_defineProperty(Bar, \"displayName\", 'Bar');\n_defineProperty(Bar, \"defaultProps\", {\n  xAxisId: 0,\n  yAxisId: 0,\n  legendType: 'rect',\n  minPointSize: 0,\n  hide: false,\n  data: [],\n  layout: 'vertical',\n  activeBar: false,\n  isAnimationActive: !Global.isSsr,\n  animationBegin: 0,\n  animationDuration: 400,\n  animationEasing: 'ease'\n});\n/**\n * Compose the data of each group\n * @param {Object} props Props for the component\n * @param {Object} item        An instance of Bar\n * @param {Array} barPosition  The offset and size of each bar\n * @param {Object} xAxis       The configuration of x-axis\n * @param {Object} yAxis       The configuration of y-axis\n * @param {Array} stackedData  The stacked data of a bar item\n * @return{Array} Composed data\n */\n_defineProperty(Bar, \"getComposedData\", function (_ref2) {\n  var props = _ref2.props,\n    item = _ref2.item,\n    barPosition = _ref2.barPosition,\n    bandSize = _ref2.bandSize,\n    xAxis = _ref2.xAxis,\n    yAxis = _ref2.yAxis,\n    xAxisTicks = _ref2.xAxisTicks,\n    yAxisTicks = _ref2.yAxisTicks,\n    stackedData = _ref2.stackedData,\n    dataStartIndex = _ref2.dataStartIndex,\n    displayedData = _ref2.displayedData,\n    offset = _ref2.offset;\n  var pos = findPositionOfBar(barPosition, item);\n  if (!pos) {\n    return null;\n  }\n  var layout = props.layout;\n  var itemDefaultProps = item.type.defaultProps;\n  var itemProps = itemDefaultProps !== undefined ? _objectSpread(_objectSpread({}, itemDefaultProps), item.props) : item.props;\n  var dataKey = itemProps.dataKey,\n    children = itemProps.children,\n    minPointSizeProp = itemProps.minPointSize;\n  var numericAxis = layout === 'horizontal' ? yAxis : xAxis;\n  var stackedDomain = stackedData ? numericAxis.scale.domain() : null;\n  var baseValue = getBaseValueOfBar({\n    numericAxis: numericAxis\n  });\n  var cells = findAllByType(children, Cell);\n  var rects = displayedData.map(function (entry, index) {\n    var value, x, y, width, height, background;\n    if (stackedData) {\n      value = truncateByDomain(stackedData[dataStartIndex + index], stackedDomain);\n    } else {\n      value = getValueByDataKey(entry, dataKey);\n      if (!Array.isArray(value)) {\n        value = [baseValue, value];\n      }\n    }\n    var minPointSize = minPointSizeCallback(minPointSizeProp, _Bar.defaultProps.minPointSize)(value[1], index);\n    if (layout === 'horizontal') {\n      var _ref4;\n      var _ref3 = [yAxis.scale(value[0]), yAxis.scale(value[1])],\n        baseValueScale = _ref3[0],\n        currentValueScale = _ref3[1];\n      x = getCateCoordinateOfBar({\n        axis: xAxis,\n        ticks: xAxisTicks,\n        bandSize: bandSize,\n        offset: pos.offset,\n        entry: entry,\n        index: index\n      });\n      y = (_ref4 = currentValueScale !== null && currentValueScale !== void 0 ? currentValueScale : baseValueScale) !== null && _ref4 !== void 0 ? _ref4 : undefined;\n      width = pos.size;\n      var computedHeight = baseValueScale - currentValueScale;\n      height = Number.isNaN(computedHeight) ? 0 : computedHeight;\n      background = {\n        x: x,\n        y: yAxis.y,\n        width: width,\n        height: yAxis.height\n      };\n      if (Math.abs(minPointSize) > 0 && Math.abs(height) < Math.abs(minPointSize)) {\n        var delta = mathSign(height || minPointSize) * (Math.abs(minPointSize) - Math.abs(height));\n        y -= delta;\n        height += delta;\n      }\n    } else {\n      var _ref5 = [xAxis.scale(value[0]), xAxis.scale(value[1])],\n        _baseValueScale = _ref5[0],\n        _currentValueScale = _ref5[1];\n      x = _baseValueScale;\n      y = getCateCoordinateOfBar({\n        axis: yAxis,\n        ticks: yAxisTicks,\n        bandSize: bandSize,\n        offset: pos.offset,\n        entry: entry,\n        index: index\n      });\n      width = _currentValueScale - _baseValueScale;\n      height = pos.size;\n      background = {\n        x: xAxis.x,\n        y: y,\n        width: xAxis.width,\n        height: height\n      };\n      if (Math.abs(minPointSize) > 0 && Math.abs(width) < Math.abs(minPointSize)) {\n        var _delta = mathSign(width || minPointSize) * (Math.abs(minPointSize) - Math.abs(width));\n        width += _delta;\n      }\n    }\n    return _objectSpread(_objectSpread(_objectSpread({}, entry), {}, {\n      x: x,\n      y: y,\n      width: width,\n      height: height,\n      value: stackedData ? value : value[1],\n      payload: entry,\n      background: background\n    }, cells && cells[index] && cells[index].props), {}, {\n      tooltipPayload: [getTooltipItem(item, entry)],\n      tooltipPosition: {\n        x: x + width / 2,\n        y: y + height / 2\n      }\n    });\n  });\n  return _objectSpread({\n    data: rects,\n    layout: layout\n  }, offset);\n});", "map": {"version": 3, "names": ["_excluded", "_Bar", "_typeof", "o", "Symbol", "iterator", "constructor", "prototype", "_objectWithoutProperties", "source", "excluded", "target", "_objectWithoutPropertiesLoose", "key", "i", "Object", "getOwnPropertySymbols", "sourceSymbolKeys", "length", "indexOf", "propertyIsEnumerable", "call", "hasOwnProperty", "_extends", "assign", "bind", "arguments", "apply", "ownKeys", "e", "r", "t", "keys", "filter", "getOwnPropertyDescriptor", "enumerable", "push", "_objectSpread", "for<PERSON>ach", "_defineProperty", "getOwnPropertyDescriptors", "defineProperties", "defineProperty", "_classCallCheck", "instance", "<PERSON><PERSON><PERSON><PERSON>", "TypeError", "_defineProperties", "props", "descriptor", "configurable", "writable", "_to<PERSON><PERSON><PERSON><PERSON><PERSON>", "_createClass", "protoProps", "staticProps", "_callSuper", "_getPrototypeOf", "_possibleConstructorReturn", "_isNativeReflectConstruct", "Reflect", "construct", "self", "_assertThisInitialized", "ReferenceError", "Boolean", "valueOf", "setPrototypeOf", "getPrototypeOf", "__proto__", "_inherits", "subClass", "superClass", "create", "value", "_setPrototypeOf", "p", "obj", "_toPrimitive", "toPrimitive", "String", "Number", "React", "PureComponent", "clsx", "Animate", "isEqual", "isNil", "Layer", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Cell", "LabelList", "uniqueId", "mathSign", "interpolateNumber", "filterProps", "findAllByType", "Global", "getCateCoordinateOfBar", "getValueByDataKey", "truncateByDomain", "getBaseValueOfBar", "findPositionOfBar", "getTooltipItem", "adaptEventsOfChild", "BarRectangle", "minPointSizeCallback", "Bar", "_PureComponent", "_this", "_len", "args", "Array", "_key", "concat", "isAnimationFinished", "onAnimationEnd", "setState", "onAnimationStart", "renderRectanglesStatically", "data", "_this2", "_this$props", "shape", "dataKey", "activeIndex", "activeBar", "baseProps", "map", "entry", "isActive", "option", "index", "handleAnimationStart", "handleAnimationEnd", "createElement", "className", "x", "y", "renderRectanglesWithAnimation", "_this3", "_this$props2", "layout", "isAnimationActive", "animationBegin", "animationDuration", "animationEasing", "animationId", "prevData", "state", "begin", "duration", "easing", "from", "to", "_ref", "stepData", "prev", "interpolatorX", "interpolatorY", "interpolatorWidth", "width", "interpolatorHeight", "height", "_interpolatorHeight", "h", "interpolator", "w", "renderRectangles", "_this$props3", "renderBackground", "_this4", "_this$props4", "backgroundProps", "background", "rest", "fill", "renderErrorBar", "needClip", "clipPathId", "_this$props5", "xAxis", "yAxis", "children", "errorBarItems", "offset", "dataPointFormatter", "dataPoint", "isArray", "errorVal", "errorBarProps", "clipPath", "item", "cloneElement", "render", "_this$props6", "hide", "left", "top", "id", "layerClass", "needClipX", "allowDataOverflow", "needClipY", "renderCallByParent", "getDerivedStateFromProps", "nextProps", "prevState", "prevAnimationId", "curData", "xAxisId", "yAxisId", "legendType", "minPointSize", "isSsr", "_ref2", "barPosition", "bandSize", "xAxisTicks", "yAxisTicks", "stackedData", "dataStartIndex", "displayedData", "pos", "itemDefaultProps", "type", "defaultProps", "itemProps", "undefined", "minPointSizeProp", "numericAxis", "stackedDomain", "scale", "domain", "baseValue", "cells", "rects", "_ref4", "_ref3", "baseValueScale", "currentValueScale", "axis", "ticks", "size", "computedHeight", "isNaN", "Math", "abs", "delta", "_ref5", "_baseValueScale", "_currentValueScale", "_delta", "payload", "tooltipPayload", "tooltipPosition"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/MVP/client/node_modules/recharts/es6/cartesian/Bar.js"], "sourcesContent": ["var _excluded = [\"value\", \"background\"];\nvar _Bar;\nfunction _typeof(o) { \"@babel/helpers - typeof\"; return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (o) { return typeof o; } : function (o) { return o && \"function\" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? \"symbol\" : typeof o; }, _typeof(o); }\nfunction _objectWithoutProperties(source, excluded) { if (source == null) return {}; var target = _objectWithoutPropertiesLoose(source, excluded); var key, i; if (Object.getOwnPropertySymbols) { var sourceSymbolKeys = Object.getOwnPropertySymbols(source); for (i = 0; i < sourceSymbolKeys.length; i++) { key = sourceSymbolKeys[i]; if (excluded.indexOf(key) >= 0) continue; if (!Object.prototype.propertyIsEnumerable.call(source, key)) continue; target[key] = source[key]; } } return target; }\nfunction _objectWithoutPropertiesLoose(source, excluded) { if (source == null) return {}; var target = {}; for (var key in source) { if (Object.prototype.hasOwnProperty.call(source, key)) { if (excluded.indexOf(key) >= 0) continue; target[key] = source[key]; } } return target; }\nfunction _extends() { _extends = Object.assign ? Object.assign.bind() : function (target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i]; for (var key in source) { if (Object.prototype.hasOwnProperty.call(source, key)) { target[key] = source[key]; } } } return target; }; return _extends.apply(this, arguments); }\nfunction ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nfunction _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError(\"Cannot call a class as a function\"); } }\nfunction _defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if (\"value\" in descriptor) descriptor.writable = true; Object.defineProperty(target, _toPropertyKey(descriptor.key), descriptor); } }\nfunction _createClass(Constructor, protoProps, staticProps) { if (protoProps) _defineProperties(Constructor.prototype, protoProps); if (staticProps) _defineProperties(Constructor, staticProps); Object.defineProperty(Constructor, \"prototype\", { writable: false }); return Constructor; }\nfunction _callSuper(t, o, e) { return o = _getPrototypeOf(o), _possibleConstructorReturn(t, _isNativeReflectConstruct() ? Reflect.construct(o, e || [], _getPrototypeOf(t).constructor) : o.apply(t, e)); }\nfunction _possibleConstructorReturn(self, call) { if (call && (_typeof(call) === \"object\" || typeof call === \"function\")) { return call; } else if (call !== void 0) { throw new TypeError(\"Derived constructors may only return object or undefined\"); } return _assertThisInitialized(self); }\nfunction _assertThisInitialized(self) { if (self === void 0) { throw new ReferenceError(\"this hasn't been initialised - super() hasn't been called\"); } return self; }\nfunction _isNativeReflectConstruct() { try { var t = !Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {})); } catch (t) {} return (_isNativeReflectConstruct = function _isNativeReflectConstruct() { return !!t; })(); }\nfunction _getPrototypeOf(o) { _getPrototypeOf = Object.setPrototypeOf ? Object.getPrototypeOf.bind() : function _getPrototypeOf(o) { return o.__proto__ || Object.getPrototypeOf(o); }; return _getPrototypeOf(o); }\nfunction _inherits(subClass, superClass) { if (typeof superClass !== \"function\" && superClass !== null) { throw new TypeError(\"Super expression must either be null or a function\"); } subClass.prototype = Object.create(superClass && superClass.prototype, { constructor: { value: subClass, writable: true, configurable: true } }); Object.defineProperty(subClass, \"prototype\", { writable: false }); if (superClass) _setPrototypeOf(subClass, superClass); }\nfunction _setPrototypeOf(o, p) { _setPrototypeOf = Object.setPrototypeOf ? Object.setPrototypeOf.bind() : function _setPrototypeOf(o, p) { o.__proto__ = p; return o; }; return _setPrototypeOf(o, p); }\nfunction _defineProperty(obj, key, value) { key = _toPropertyKey(key); if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }\nfunction _toPropertyKey(t) { var i = _toPrimitive(t, \"string\"); return \"symbol\" == _typeof(i) ? i : i + \"\"; }\nfunction _toPrimitive(t, r) { if (\"object\" != _typeof(t) || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || \"default\"); if (\"object\" != _typeof(i)) return i; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (\"string\" === r ? String : Number)(t); }\n/**\n * @fileOverview Render a group of bar\n */\nimport React, { PureComponent } from 'react';\nimport clsx from 'clsx';\nimport Animate from 'react-smooth';\nimport isEqual from 'lodash/isEqual';\nimport isNil from 'lodash/isNil';\nimport { Layer } from '../container/Layer';\nimport { ErrorBar } from './ErrorBar';\nimport { Cell } from '../component/Cell';\nimport { LabelList } from '../component/LabelList';\nimport { uniqueId, mathSign, interpolateNumber } from '../util/DataUtils';\nimport { filterProps, findAllByType } from '../util/ReactUtils';\nimport { Global } from '../util/Global';\nimport { getCateCoordinateOfBar, getValueByDataKey, truncateByDomain, getBaseValueOfBar, findPositionOfBar, getTooltipItem } from '../util/ChartUtils';\nimport { adaptEventsOfChild } from '../util/types';\nimport { BarRectangle, minPointSizeCallback } from '../util/BarUtils';\nexport var Bar = /*#__PURE__*/function (_PureComponent) {\n  function Bar() {\n    var _this;\n    _classCallCheck(this, Bar);\n    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n      args[_key] = arguments[_key];\n    }\n    _this = _callSuper(this, Bar, [].concat(args));\n    _defineProperty(_this, \"state\", {\n      isAnimationFinished: false\n    });\n    _defineProperty(_this, \"id\", uniqueId('recharts-bar-'));\n    _defineProperty(_this, \"handleAnimationEnd\", function () {\n      var onAnimationEnd = _this.props.onAnimationEnd;\n      _this.setState({\n        isAnimationFinished: true\n      });\n      if (onAnimationEnd) {\n        onAnimationEnd();\n      }\n    });\n    _defineProperty(_this, \"handleAnimationStart\", function () {\n      var onAnimationStart = _this.props.onAnimationStart;\n      _this.setState({\n        isAnimationFinished: false\n      });\n      if (onAnimationStart) {\n        onAnimationStart();\n      }\n    });\n    return _this;\n  }\n  _inherits(Bar, _PureComponent);\n  return _createClass(Bar, [{\n    key: \"renderRectanglesStatically\",\n    value: function renderRectanglesStatically(data) {\n      var _this2 = this;\n      var _this$props = this.props,\n        shape = _this$props.shape,\n        dataKey = _this$props.dataKey,\n        activeIndex = _this$props.activeIndex,\n        activeBar = _this$props.activeBar;\n      var baseProps = filterProps(this.props, false);\n      return data && data.map(function (entry, i) {\n        var isActive = i === activeIndex;\n        var option = isActive ? activeBar : shape;\n        var props = _objectSpread(_objectSpread(_objectSpread({}, baseProps), entry), {}, {\n          isActive: isActive,\n          option: option,\n          index: i,\n          dataKey: dataKey,\n          onAnimationStart: _this2.handleAnimationStart,\n          onAnimationEnd: _this2.handleAnimationEnd\n        });\n        return /*#__PURE__*/React.createElement(Layer, _extends({\n          className: \"recharts-bar-rectangle\"\n        }, adaptEventsOfChild(_this2.props, entry, i), {\n          // https://github.com/recharts/recharts/issues/5415\n          // eslint-disable-next-line react/no-array-index-key\n          key: \"rectangle-\".concat(entry === null || entry === void 0 ? void 0 : entry.x, \"-\").concat(entry === null || entry === void 0 ? void 0 : entry.y, \"-\").concat(entry === null || entry === void 0 ? void 0 : entry.value, \"-\").concat(i)\n        }), /*#__PURE__*/React.createElement(BarRectangle, props));\n      });\n    }\n  }, {\n    key: \"renderRectanglesWithAnimation\",\n    value: function renderRectanglesWithAnimation() {\n      var _this3 = this;\n      var _this$props2 = this.props,\n        data = _this$props2.data,\n        layout = _this$props2.layout,\n        isAnimationActive = _this$props2.isAnimationActive,\n        animationBegin = _this$props2.animationBegin,\n        animationDuration = _this$props2.animationDuration,\n        animationEasing = _this$props2.animationEasing,\n        animationId = _this$props2.animationId;\n      var prevData = this.state.prevData;\n      return /*#__PURE__*/React.createElement(Animate, {\n        begin: animationBegin,\n        duration: animationDuration,\n        isActive: isAnimationActive,\n        easing: animationEasing,\n        from: {\n          t: 0\n        },\n        to: {\n          t: 1\n        },\n        key: \"bar-\".concat(animationId),\n        onAnimationEnd: this.handleAnimationEnd,\n        onAnimationStart: this.handleAnimationStart\n      }, function (_ref) {\n        var t = _ref.t;\n        var stepData = data.map(function (entry, index) {\n          var prev = prevData && prevData[index];\n          if (prev) {\n            var interpolatorX = interpolateNumber(prev.x, entry.x);\n            var interpolatorY = interpolateNumber(prev.y, entry.y);\n            var interpolatorWidth = interpolateNumber(prev.width, entry.width);\n            var interpolatorHeight = interpolateNumber(prev.height, entry.height);\n            return _objectSpread(_objectSpread({}, entry), {}, {\n              x: interpolatorX(t),\n              y: interpolatorY(t),\n              width: interpolatorWidth(t),\n              height: interpolatorHeight(t)\n            });\n          }\n          if (layout === 'horizontal') {\n            var _interpolatorHeight = interpolateNumber(0, entry.height);\n            var h = _interpolatorHeight(t);\n            return _objectSpread(_objectSpread({}, entry), {}, {\n              y: entry.y + entry.height - h,\n              height: h\n            });\n          }\n          var interpolator = interpolateNumber(0, entry.width);\n          var w = interpolator(t);\n          return _objectSpread(_objectSpread({}, entry), {}, {\n            width: w\n          });\n        });\n        return /*#__PURE__*/React.createElement(Layer, null, _this3.renderRectanglesStatically(stepData));\n      });\n    }\n  }, {\n    key: \"renderRectangles\",\n    value: function renderRectangles() {\n      var _this$props3 = this.props,\n        data = _this$props3.data,\n        isAnimationActive = _this$props3.isAnimationActive;\n      var prevData = this.state.prevData;\n      if (isAnimationActive && data && data.length && (!prevData || !isEqual(prevData, data))) {\n        return this.renderRectanglesWithAnimation();\n      }\n      return this.renderRectanglesStatically(data);\n    }\n  }, {\n    key: \"renderBackground\",\n    value: function renderBackground() {\n      var _this4 = this;\n      var _this$props4 = this.props,\n        data = _this$props4.data,\n        dataKey = _this$props4.dataKey,\n        activeIndex = _this$props4.activeIndex;\n      var backgroundProps = filterProps(this.props.background, false);\n      return data.map(function (entry, i) {\n        var value = entry.value,\n          background = entry.background,\n          rest = _objectWithoutProperties(entry, _excluded);\n        if (!background) {\n          return null;\n        }\n        var props = _objectSpread(_objectSpread(_objectSpread(_objectSpread(_objectSpread({}, rest), {}, {\n          fill: '#eee'\n        }, background), backgroundProps), adaptEventsOfChild(_this4.props, entry, i)), {}, {\n          onAnimationStart: _this4.handleAnimationStart,\n          onAnimationEnd: _this4.handleAnimationEnd,\n          dataKey: dataKey,\n          index: i,\n          className: 'recharts-bar-background-rectangle'\n        });\n        return /*#__PURE__*/React.createElement(BarRectangle, _extends({\n          key: \"background-bar-\".concat(i),\n          option: _this4.props.background,\n          isActive: i === activeIndex\n        }, props));\n      });\n    }\n  }, {\n    key: \"renderErrorBar\",\n    value: function renderErrorBar(needClip, clipPathId) {\n      if (this.props.isAnimationActive && !this.state.isAnimationFinished) {\n        return null;\n      }\n      var _this$props5 = this.props,\n        data = _this$props5.data,\n        xAxis = _this$props5.xAxis,\n        yAxis = _this$props5.yAxis,\n        layout = _this$props5.layout,\n        children = _this$props5.children;\n      var errorBarItems = findAllByType(children, ErrorBar);\n      if (!errorBarItems) {\n        return null;\n      }\n      var offset = layout === 'vertical' ? data[0].height / 2 : data[0].width / 2;\n      var dataPointFormatter = function dataPointFormatter(dataPoint, dataKey) {\n        /**\n         * if the value coming from `getComposedData` is an array then this is a stacked bar chart.\n         * arr[1] represents end value of the bar since the data is in the form of [startValue, endValue].\n         * */\n        var value = Array.isArray(dataPoint.value) ? dataPoint.value[1] : dataPoint.value;\n        return {\n          x: dataPoint.x,\n          y: dataPoint.y,\n          value: value,\n          errorVal: getValueByDataKey(dataPoint, dataKey)\n        };\n      };\n      var errorBarProps = {\n        clipPath: needClip ? \"url(#clipPath-\".concat(clipPathId, \")\") : null\n      };\n      return /*#__PURE__*/React.createElement(Layer, errorBarProps, errorBarItems.map(function (item) {\n        return /*#__PURE__*/React.cloneElement(item, {\n          key: \"error-bar-\".concat(clipPathId, \"-\").concat(item.props.dataKey),\n          data: data,\n          xAxis: xAxis,\n          yAxis: yAxis,\n          layout: layout,\n          offset: offset,\n          dataPointFormatter: dataPointFormatter\n        });\n      }));\n    }\n  }, {\n    key: \"render\",\n    value: function render() {\n      var _this$props6 = this.props,\n        hide = _this$props6.hide,\n        data = _this$props6.data,\n        className = _this$props6.className,\n        xAxis = _this$props6.xAxis,\n        yAxis = _this$props6.yAxis,\n        left = _this$props6.left,\n        top = _this$props6.top,\n        width = _this$props6.width,\n        height = _this$props6.height,\n        isAnimationActive = _this$props6.isAnimationActive,\n        background = _this$props6.background,\n        id = _this$props6.id;\n      if (hide || !data || !data.length) {\n        return null;\n      }\n      var isAnimationFinished = this.state.isAnimationFinished;\n      var layerClass = clsx('recharts-bar', className);\n      var needClipX = xAxis && xAxis.allowDataOverflow;\n      var needClipY = yAxis && yAxis.allowDataOverflow;\n      var needClip = needClipX || needClipY;\n      var clipPathId = isNil(id) ? this.id : id;\n      return /*#__PURE__*/React.createElement(Layer, {\n        className: layerClass\n      }, needClipX || needClipY ? /*#__PURE__*/React.createElement(\"defs\", null, /*#__PURE__*/React.createElement(\"clipPath\", {\n        id: \"clipPath-\".concat(clipPathId)\n      }, /*#__PURE__*/React.createElement(\"rect\", {\n        x: needClipX ? left : left - width / 2,\n        y: needClipY ? top : top - height / 2,\n        width: needClipX ? width : width * 2,\n        height: needClipY ? height : height * 2\n      }))) : null, /*#__PURE__*/React.createElement(Layer, {\n        className: \"recharts-bar-rectangles\",\n        clipPath: needClip ? \"url(#clipPath-\".concat(clipPathId, \")\") : null\n      }, background ? this.renderBackground() : null, this.renderRectangles()), this.renderErrorBar(needClip, clipPathId), (!isAnimationActive || isAnimationFinished) && LabelList.renderCallByParent(this.props, data));\n    }\n  }], [{\n    key: \"getDerivedStateFromProps\",\n    value: function getDerivedStateFromProps(nextProps, prevState) {\n      if (nextProps.animationId !== prevState.prevAnimationId) {\n        return {\n          prevAnimationId: nextProps.animationId,\n          curData: nextProps.data,\n          prevData: prevState.curData\n        };\n      }\n      if (nextProps.data !== prevState.curData) {\n        return {\n          curData: nextProps.data\n        };\n      }\n      return null;\n    }\n  }]);\n}(PureComponent);\n_Bar = Bar;\n_defineProperty(Bar, \"displayName\", 'Bar');\n_defineProperty(Bar, \"defaultProps\", {\n  xAxisId: 0,\n  yAxisId: 0,\n  legendType: 'rect',\n  minPointSize: 0,\n  hide: false,\n  data: [],\n  layout: 'vertical',\n  activeBar: false,\n  isAnimationActive: !Global.isSsr,\n  animationBegin: 0,\n  animationDuration: 400,\n  animationEasing: 'ease'\n});\n/**\n * Compose the data of each group\n * @param {Object} props Props for the component\n * @param {Object} item        An instance of Bar\n * @param {Array} barPosition  The offset and size of each bar\n * @param {Object} xAxis       The configuration of x-axis\n * @param {Object} yAxis       The configuration of y-axis\n * @param {Array} stackedData  The stacked data of a bar item\n * @return{Array} Composed data\n */\n_defineProperty(Bar, \"getComposedData\", function (_ref2) {\n  var props = _ref2.props,\n    item = _ref2.item,\n    barPosition = _ref2.barPosition,\n    bandSize = _ref2.bandSize,\n    xAxis = _ref2.xAxis,\n    yAxis = _ref2.yAxis,\n    xAxisTicks = _ref2.xAxisTicks,\n    yAxisTicks = _ref2.yAxisTicks,\n    stackedData = _ref2.stackedData,\n    dataStartIndex = _ref2.dataStartIndex,\n    displayedData = _ref2.displayedData,\n    offset = _ref2.offset;\n  var pos = findPositionOfBar(barPosition, item);\n  if (!pos) {\n    return null;\n  }\n  var layout = props.layout;\n  var itemDefaultProps = item.type.defaultProps;\n  var itemProps = itemDefaultProps !== undefined ? _objectSpread(_objectSpread({}, itemDefaultProps), item.props) : item.props;\n  var dataKey = itemProps.dataKey,\n    children = itemProps.children,\n    minPointSizeProp = itemProps.minPointSize;\n  var numericAxis = layout === 'horizontal' ? yAxis : xAxis;\n  var stackedDomain = stackedData ? numericAxis.scale.domain() : null;\n  var baseValue = getBaseValueOfBar({\n    numericAxis: numericAxis\n  });\n  var cells = findAllByType(children, Cell);\n  var rects = displayedData.map(function (entry, index) {\n    var value, x, y, width, height, background;\n    if (stackedData) {\n      value = truncateByDomain(stackedData[dataStartIndex + index], stackedDomain);\n    } else {\n      value = getValueByDataKey(entry, dataKey);\n      if (!Array.isArray(value)) {\n        value = [baseValue, value];\n      }\n    }\n    var minPointSize = minPointSizeCallback(minPointSizeProp, _Bar.defaultProps.minPointSize)(value[1], index);\n    if (layout === 'horizontal') {\n      var _ref4;\n      var _ref3 = [yAxis.scale(value[0]), yAxis.scale(value[1])],\n        baseValueScale = _ref3[0],\n        currentValueScale = _ref3[1];\n      x = getCateCoordinateOfBar({\n        axis: xAxis,\n        ticks: xAxisTicks,\n        bandSize: bandSize,\n        offset: pos.offset,\n        entry: entry,\n        index: index\n      });\n      y = (_ref4 = currentValueScale !== null && currentValueScale !== void 0 ? currentValueScale : baseValueScale) !== null && _ref4 !== void 0 ? _ref4 : undefined;\n      width = pos.size;\n      var computedHeight = baseValueScale - currentValueScale;\n      height = Number.isNaN(computedHeight) ? 0 : computedHeight;\n      background = {\n        x: x,\n        y: yAxis.y,\n        width: width,\n        height: yAxis.height\n      };\n      if (Math.abs(minPointSize) > 0 && Math.abs(height) < Math.abs(minPointSize)) {\n        var delta = mathSign(height || minPointSize) * (Math.abs(minPointSize) - Math.abs(height));\n        y -= delta;\n        height += delta;\n      }\n    } else {\n      var _ref5 = [xAxis.scale(value[0]), xAxis.scale(value[1])],\n        _baseValueScale = _ref5[0],\n        _currentValueScale = _ref5[1];\n      x = _baseValueScale;\n      y = getCateCoordinateOfBar({\n        axis: yAxis,\n        ticks: yAxisTicks,\n        bandSize: bandSize,\n        offset: pos.offset,\n        entry: entry,\n        index: index\n      });\n      width = _currentValueScale - _baseValueScale;\n      height = pos.size;\n      background = {\n        x: xAxis.x,\n        y: y,\n        width: xAxis.width,\n        height: height\n      };\n      if (Math.abs(minPointSize) > 0 && Math.abs(width) < Math.abs(minPointSize)) {\n        var _delta = mathSign(width || minPointSize) * (Math.abs(minPointSize) - Math.abs(width));\n        width += _delta;\n      }\n    }\n    return _objectSpread(_objectSpread(_objectSpread({}, entry), {}, {\n      x: x,\n      y: y,\n      width: width,\n      height: height,\n      value: stackedData ? value : value[1],\n      payload: entry,\n      background: background\n    }, cells && cells[index] && cells[index].props), {}, {\n      tooltipPayload: [getTooltipItem(item, entry)],\n      tooltipPosition: {\n        x: x + width / 2,\n        y: y + height / 2\n      }\n    });\n  });\n  return _objectSpread({\n    data: rects,\n    layout: layout\n  }, offset);\n});"], "mappings": "AAAA,IAAIA,SAAS,GAAG,CAAC,OAAO,EAAE,YAAY,CAAC;AACvC,IAAIC,IAAI;AACR,SAASC,OAAOA,CAACC,CAAC,EAAE;EAAE,yBAAyB;;EAAE,OAAOD,OAAO,GAAG,UAAU,IAAI,OAAOE,MAAM,IAAI,QAAQ,IAAI,OAAOA,MAAM,CAACC,QAAQ,GAAG,UAAUF,CAAC,EAAE;IAAE,OAAO,OAAOA,CAAC;EAAE,CAAC,GAAG,UAAUA,CAAC,EAAE;IAAE,OAAOA,CAAC,IAAI,UAAU,IAAI,OAAOC,MAAM,IAAID,CAAC,CAACG,WAAW,KAAKF,MAAM,IAAID,CAAC,KAAKC,MAAM,CAACG,SAAS,GAAG,QAAQ,GAAG,OAAOJ,CAAC;EAAE,CAAC,EAAED,OAAO,CAACC,CAAC,CAAC;AAAE;AAC7T,SAASK,wBAAwBA,CAACC,MAAM,EAAEC,QAAQ,EAAE;EAAE,IAAID,MAAM,IAAI,IAAI,EAAE,OAAO,CAAC,CAAC;EAAE,IAAIE,MAAM,GAAGC,6BAA6B,CAACH,MAAM,EAAEC,QAAQ,CAAC;EAAE,IAAIG,GAAG,EAAEC,CAAC;EAAE,IAAIC,MAAM,CAACC,qBAAqB,EAAE;IAAE,IAAIC,gBAAgB,GAAGF,MAAM,CAACC,qBAAqB,CAACP,MAAM,CAAC;IAAE,KAAKK,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGG,gBAAgB,CAACC,MAAM,EAAEJ,CAAC,EAAE,EAAE;MAAED,GAAG,GAAGI,gBAAgB,CAACH,CAAC,CAAC;MAAE,IAAIJ,QAAQ,CAACS,OAAO,CAACN,GAAG,CAAC,IAAI,CAAC,EAAE;MAAU,IAAI,CAACE,MAAM,CAACR,SAAS,CAACa,oBAAoB,CAACC,IAAI,CAACZ,MAAM,EAAEI,GAAG,CAAC,EAAE;MAAUF,MAAM,CAACE,GAAG,CAAC,GAAGJ,MAAM,CAACI,GAAG,CAAC;IAAE;EAAE;EAAE,OAAOF,MAAM;AAAE;AAC3e,SAASC,6BAA6BA,CAACH,MAAM,EAAEC,QAAQ,EAAE;EAAE,IAAID,MAAM,IAAI,IAAI,EAAE,OAAO,CAAC,CAAC;EAAE,IAAIE,MAAM,GAAG,CAAC,CAAC;EAAE,KAAK,IAAIE,GAAG,IAAIJ,MAAM,EAAE;IAAE,IAAIM,MAAM,CAACR,SAAS,CAACe,cAAc,CAACD,IAAI,CAACZ,MAAM,EAAEI,GAAG,CAAC,EAAE;MAAE,IAAIH,QAAQ,CAACS,OAAO,CAACN,GAAG,CAAC,IAAI,CAAC,EAAE;MAAUF,MAAM,CAACE,GAAG,CAAC,GAAGJ,MAAM,CAACI,GAAG,CAAC;IAAE;EAAE;EAAE,OAAOF,MAAM;AAAE;AACtR,SAASY,QAAQA,CAAA,EAAG;EAAEA,QAAQ,GAAGR,MAAM,CAACS,MAAM,GAAGT,MAAM,CAACS,MAAM,CAACC,IAAI,CAAC,CAAC,GAAG,UAAUd,MAAM,EAAE;IAAE,KAAK,IAAIG,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGY,SAAS,CAACR,MAAM,EAAEJ,CAAC,EAAE,EAAE;MAAE,IAAIL,MAAM,GAAGiB,SAAS,CAACZ,CAAC,CAAC;MAAE,KAAK,IAAID,GAAG,IAAIJ,MAAM,EAAE;QAAE,IAAIM,MAAM,CAACR,SAAS,CAACe,cAAc,CAACD,IAAI,CAACZ,MAAM,EAAEI,GAAG,CAAC,EAAE;UAAEF,MAAM,CAACE,GAAG,CAAC,GAAGJ,MAAM,CAACI,GAAG,CAAC;QAAE;MAAE;IAAE;IAAE,OAAOF,MAAM;EAAE,CAAC;EAAE,OAAOY,QAAQ,CAACI,KAAK,CAAC,IAAI,EAAED,SAAS,CAAC;AAAE;AAClV,SAASE,OAAOA,CAACC,CAAC,EAAEC,CAAC,EAAE;EAAE,IAAIC,CAAC,GAAGhB,MAAM,CAACiB,IAAI,CAACH,CAAC,CAAC;EAAE,IAAId,MAAM,CAACC,qBAAqB,EAAE;IAAE,IAAIb,CAAC,GAAGY,MAAM,CAACC,qBAAqB,CAACa,CAAC,CAAC;IAAEC,CAAC,KAAK3B,CAAC,GAAGA,CAAC,CAAC8B,MAAM,CAAC,UAAUH,CAAC,EAAE;MAAE,OAAOf,MAAM,CAACmB,wBAAwB,CAACL,CAAC,EAAEC,CAAC,CAAC,CAACK,UAAU;IAAE,CAAC,CAAC,CAAC,EAAEJ,CAAC,CAACK,IAAI,CAACT,KAAK,CAACI,CAAC,EAAE5B,CAAC,CAAC;EAAE;EAAE,OAAO4B,CAAC;AAAE;AAC9P,SAASM,aAAaA,CAACR,CAAC,EAAE;EAAE,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGJ,SAAS,CAACR,MAAM,EAAEY,CAAC,EAAE,EAAE;IAAE,IAAIC,CAAC,GAAG,IAAI,IAAIL,SAAS,CAACI,CAAC,CAAC,GAAGJ,SAAS,CAACI,CAAC,CAAC,GAAG,CAAC,CAAC;IAAEA,CAAC,GAAG,CAAC,GAAGF,OAAO,CAACb,MAAM,CAACgB,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAACO,OAAO,CAAC,UAAUR,CAAC,EAAE;MAAES,eAAe,CAACV,CAAC,EAAEC,CAAC,EAAEC,CAAC,CAACD,CAAC,CAAC,CAAC;IAAE,CAAC,CAAC,GAAGf,MAAM,CAACyB,yBAAyB,GAAGzB,MAAM,CAAC0B,gBAAgB,CAACZ,CAAC,EAAEd,MAAM,CAACyB,yBAAyB,CAACT,CAAC,CAAC,CAAC,GAAGH,OAAO,CAACb,MAAM,CAACgB,CAAC,CAAC,CAAC,CAACO,OAAO,CAAC,UAAUR,CAAC,EAAE;MAAEf,MAAM,CAAC2B,cAAc,CAACb,CAAC,EAAEC,CAAC,EAAEf,MAAM,CAACmB,wBAAwB,CAACH,CAAC,EAAED,CAAC,CAAC,CAAC;IAAE,CAAC,CAAC;EAAE;EAAE,OAAOD,CAAC;AAAE;AACtb,SAASc,eAAeA,CAACC,QAAQ,EAAEC,WAAW,EAAE;EAAE,IAAI,EAAED,QAAQ,YAAYC,WAAW,CAAC,EAAE;IAAE,MAAM,IAAIC,SAAS,CAAC,mCAAmC,CAAC;EAAE;AAAE;AACxJ,SAASC,iBAAiBA,CAACpC,MAAM,EAAEqC,KAAK,EAAE;EAAE,KAAK,IAAIlC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGkC,KAAK,CAAC9B,MAAM,EAAEJ,CAAC,EAAE,EAAE;IAAE,IAAImC,UAAU,GAAGD,KAAK,CAAClC,CAAC,CAAC;IAAEmC,UAAU,CAACd,UAAU,GAAGc,UAAU,CAACd,UAAU,IAAI,KAAK;IAAEc,UAAU,CAACC,YAAY,GAAG,IAAI;IAAE,IAAI,OAAO,IAAID,UAAU,EAAEA,UAAU,CAACE,QAAQ,GAAG,IAAI;IAAEpC,MAAM,CAAC2B,cAAc,CAAC/B,MAAM,EAAEyC,cAAc,CAACH,UAAU,CAACpC,GAAG,CAAC,EAAEoC,UAAU,CAAC;EAAE;AAAE;AAC5U,SAASI,YAAYA,CAACR,WAAW,EAAES,UAAU,EAAEC,WAAW,EAAE;EAAE,IAAID,UAAU,EAAEP,iBAAiB,CAACF,WAAW,CAACtC,SAAS,EAAE+C,UAAU,CAAC;EAAE,IAAIC,WAAW,EAAER,iBAAiB,CAACF,WAAW,EAAEU,WAAW,CAAC;EAAExC,MAAM,CAAC2B,cAAc,CAACG,WAAW,EAAE,WAAW,EAAE;IAAEM,QAAQ,EAAE;EAAM,CAAC,CAAC;EAAE,OAAON,WAAW;AAAE;AAC5R,SAASW,UAAUA,CAACzB,CAAC,EAAE5B,CAAC,EAAE0B,CAAC,EAAE;EAAE,OAAO1B,CAAC,GAAGsD,eAAe,CAACtD,CAAC,CAAC,EAAEuD,0BAA0B,CAAC3B,CAAC,EAAE4B,yBAAyB,CAAC,CAAC,GAAGC,OAAO,CAACC,SAAS,CAAC1D,CAAC,EAAE0B,CAAC,IAAI,EAAE,EAAE4B,eAAe,CAAC1B,CAAC,CAAC,CAACzB,WAAW,CAAC,GAAGH,CAAC,CAACwB,KAAK,CAACI,CAAC,EAAEF,CAAC,CAAC,CAAC;AAAE;AAC1M,SAAS6B,0BAA0BA,CAACI,IAAI,EAAEzC,IAAI,EAAE;EAAE,IAAIA,IAAI,KAAKnB,OAAO,CAACmB,IAAI,CAAC,KAAK,QAAQ,IAAI,OAAOA,IAAI,KAAK,UAAU,CAAC,EAAE;IAAE,OAAOA,IAAI;EAAE,CAAC,MAAM,IAAIA,IAAI,KAAK,KAAK,CAAC,EAAE;IAAE,MAAM,IAAIyB,SAAS,CAAC,0DAA0D,CAAC;EAAE;EAAE,OAAOiB,sBAAsB,CAACD,IAAI,CAAC;AAAE;AAC/R,SAASC,sBAAsBA,CAACD,IAAI,EAAE;EAAE,IAAIA,IAAI,KAAK,KAAK,CAAC,EAAE;IAAE,MAAM,IAAIE,cAAc,CAAC,2DAA2D,CAAC;EAAE;EAAE,OAAOF,IAAI;AAAE;AACrK,SAASH,yBAAyBA,CAAA,EAAG;EAAE,IAAI;IAAE,IAAI5B,CAAC,GAAG,CAACkC,OAAO,CAAC1D,SAAS,CAAC2D,OAAO,CAAC7C,IAAI,CAACuC,OAAO,CAACC,SAAS,CAACI,OAAO,EAAE,EAAE,EAAE,YAAY,CAAC,CAAC,CAAC,CAAC;EAAE,CAAC,CAAC,OAAOlC,CAAC,EAAE,CAAC;EAAE,OAAO,CAAC4B,yBAAyB,GAAG,SAASA,yBAAyBA,CAAA,EAAG;IAAE,OAAO,CAAC,CAAC5B,CAAC;EAAE,CAAC,EAAE,CAAC;AAAE;AAClP,SAAS0B,eAAeA,CAACtD,CAAC,EAAE;EAAEsD,eAAe,GAAG1C,MAAM,CAACoD,cAAc,GAAGpD,MAAM,CAACqD,cAAc,CAAC3C,IAAI,CAAC,CAAC,GAAG,SAASgC,eAAeA,CAACtD,CAAC,EAAE;IAAE,OAAOA,CAAC,CAACkE,SAAS,IAAItD,MAAM,CAACqD,cAAc,CAACjE,CAAC,CAAC;EAAE,CAAC;EAAE,OAAOsD,eAAe,CAACtD,CAAC,CAAC;AAAE;AACnN,SAASmE,SAASA,CAACC,QAAQ,EAAEC,UAAU,EAAE;EAAE,IAAI,OAAOA,UAAU,KAAK,UAAU,IAAIA,UAAU,KAAK,IAAI,EAAE;IAAE,MAAM,IAAI1B,SAAS,CAAC,oDAAoD,CAAC;EAAE;EAAEyB,QAAQ,CAAChE,SAAS,GAAGQ,MAAM,CAAC0D,MAAM,CAACD,UAAU,IAAIA,UAAU,CAACjE,SAAS,EAAE;IAAED,WAAW,EAAE;MAAEoE,KAAK,EAAEH,QAAQ;MAAEpB,QAAQ,EAAE,IAAI;MAAED,YAAY,EAAE;IAAK;EAAE,CAAC,CAAC;EAAEnC,MAAM,CAAC2B,cAAc,CAAC6B,QAAQ,EAAE,WAAW,EAAE;IAAEpB,QAAQ,EAAE;EAAM,CAAC,CAAC;EAAE,IAAIqB,UAAU,EAAEG,eAAe,CAACJ,QAAQ,EAAEC,UAAU,CAAC;AAAE;AACnc,SAASG,eAAeA,CAACxE,CAAC,EAAEyE,CAAC,EAAE;EAAED,eAAe,GAAG5D,MAAM,CAACoD,cAAc,GAAGpD,MAAM,CAACoD,cAAc,CAAC1C,IAAI,CAAC,CAAC,GAAG,SAASkD,eAAeA,CAACxE,CAAC,EAAEyE,CAAC,EAAE;IAAEzE,CAAC,CAACkE,SAAS,GAAGO,CAAC;IAAE,OAAOzE,CAAC;EAAE,CAAC;EAAE,OAAOwE,eAAe,CAACxE,CAAC,EAAEyE,CAAC,CAAC;AAAE;AACvM,SAASrC,eAAeA,CAACsC,GAAG,EAAEhE,GAAG,EAAE6D,KAAK,EAAE;EAAE7D,GAAG,GAAGuC,cAAc,CAACvC,GAAG,CAAC;EAAE,IAAIA,GAAG,IAAIgE,GAAG,EAAE;IAAE9D,MAAM,CAAC2B,cAAc,CAACmC,GAAG,EAAEhE,GAAG,EAAE;MAAE6D,KAAK,EAAEA,KAAK;MAAEvC,UAAU,EAAE,IAAI;MAAEe,YAAY,EAAE,IAAI;MAAEC,QAAQ,EAAE;IAAK,CAAC,CAAC;EAAE,CAAC,MAAM;IAAE0B,GAAG,CAAChE,GAAG,CAAC,GAAG6D,KAAK;EAAE;EAAE,OAAOG,GAAG;AAAE;AAC3O,SAASzB,cAAcA,CAACrB,CAAC,EAAE;EAAE,IAAIjB,CAAC,GAAGgE,YAAY,CAAC/C,CAAC,EAAE,QAAQ,CAAC;EAAE,OAAO,QAAQ,IAAI7B,OAAO,CAACY,CAAC,CAAC,GAAGA,CAAC,GAAGA,CAAC,GAAG,EAAE;AAAE;AAC5G,SAASgE,YAAYA,CAAC/C,CAAC,EAAED,CAAC,EAAE;EAAE,IAAI,QAAQ,IAAI5B,OAAO,CAAC6B,CAAC,CAAC,IAAI,CAACA,CAAC,EAAE,OAAOA,CAAC;EAAE,IAAIF,CAAC,GAAGE,CAAC,CAAC3B,MAAM,CAAC2E,WAAW,CAAC;EAAE,IAAI,KAAK,CAAC,KAAKlD,CAAC,EAAE;IAAE,IAAIf,CAAC,GAAGe,CAAC,CAACR,IAAI,CAACU,CAAC,EAAED,CAAC,IAAI,SAAS,CAAC;IAAE,IAAI,QAAQ,IAAI5B,OAAO,CAACY,CAAC,CAAC,EAAE,OAAOA,CAAC;IAAE,MAAM,IAAIgC,SAAS,CAAC,8CAA8C,CAAC;EAAE;EAAE,OAAO,CAAC,QAAQ,KAAKhB,CAAC,GAAGkD,MAAM,GAAGC,MAAM,EAAElD,CAAC,CAAC;AAAE;AAC3T;AACA;AACA;AACA,OAAOmD,KAAK,IAAIC,aAAa,QAAQ,OAAO;AAC5C,OAAOC,IAAI,MAAM,MAAM;AACvB,OAAOC,OAAO,MAAM,cAAc;AAClC,OAAOC,OAAO,MAAM,gBAAgB;AACpC,OAAOC,KAAK,MAAM,cAAc;AAChC,SAASC,KAAK,QAAQ,oBAAoB;AAC1C,SAASC,QAAQ,QAAQ,YAAY;AACrC,SAASC,IAAI,QAAQ,mBAAmB;AACxC,SAASC,SAAS,QAAQ,wBAAwB;AAClD,SAASC,QAAQ,EAAEC,QAAQ,EAAEC,iBAAiB,QAAQ,mBAAmB;AACzE,SAASC,WAAW,EAAEC,aAAa,QAAQ,oBAAoB;AAC/D,SAASC,MAAM,QAAQ,gBAAgB;AACvC,SAASC,sBAAsB,EAAEC,iBAAiB,EAAEC,gBAAgB,EAAEC,iBAAiB,EAAEC,iBAAiB,EAAEC,cAAc,QAAQ,oBAAoB;AACtJ,SAASC,kBAAkB,QAAQ,eAAe;AAClD,SAASC,YAAY,EAAEC,oBAAoB,QAAQ,kBAAkB;AACrE,OAAO,IAAIC,GAAG,GAAG,aAAa,UAAUC,cAAc,EAAE;EACtD,SAASD,GAAGA,CAAA,EAAG;IACb,IAAIE,KAAK;IACTlE,eAAe,CAAC,IAAI,EAAEgE,GAAG,CAAC;IAC1B,KAAK,IAAIG,IAAI,GAAGpF,SAAS,CAACR,MAAM,EAAE6F,IAAI,GAAG,IAAIC,KAAK,CAACF,IAAI,CAAC,EAAEG,IAAI,GAAG,CAAC,EAAEA,IAAI,GAAGH,IAAI,EAAEG,IAAI,EAAE,EAAE;MACvFF,IAAI,CAACE,IAAI,CAAC,GAAGvF,SAAS,CAACuF,IAAI,CAAC;IAC9B;IACAJ,KAAK,GAAGrD,UAAU,CAAC,IAAI,EAAEmD,GAAG,EAAE,EAAE,CAACO,MAAM,CAACH,IAAI,CAAC,CAAC;IAC9CxE,eAAe,CAACsE,KAAK,EAAE,OAAO,EAAE;MAC9BM,mBAAmB,EAAE;IACvB,CAAC,CAAC;IACF5E,eAAe,CAACsE,KAAK,EAAE,IAAI,EAAEjB,QAAQ,CAAC,eAAe,CAAC,CAAC;IACvDrD,eAAe,CAACsE,KAAK,EAAE,oBAAoB,EAAE,YAAY;MACvD,IAAIO,cAAc,GAAGP,KAAK,CAAC7D,KAAK,CAACoE,cAAc;MAC/CP,KAAK,CAACQ,QAAQ,CAAC;QACbF,mBAAmB,EAAE;MACvB,CAAC,CAAC;MACF,IAAIC,cAAc,EAAE;QAClBA,cAAc,CAAC,CAAC;MAClB;IACF,CAAC,CAAC;IACF7E,eAAe,CAACsE,KAAK,EAAE,sBAAsB,EAAE,YAAY;MACzD,IAAIS,gBAAgB,GAAGT,KAAK,CAAC7D,KAAK,CAACsE,gBAAgB;MACnDT,KAAK,CAACQ,QAAQ,CAAC;QACbF,mBAAmB,EAAE;MACvB,CAAC,CAAC;MACF,IAAIG,gBAAgB,EAAE;QACpBA,gBAAgB,CAAC,CAAC;MACpB;IACF,CAAC,CAAC;IACF,OAAOT,KAAK;EACd;EACAvC,SAAS,CAACqC,GAAG,EAAEC,cAAc,CAAC;EAC9B,OAAOvD,YAAY,CAACsD,GAAG,EAAE,CAAC;IACxB9F,GAAG,EAAE,4BAA4B;IACjC6D,KAAK,EAAE,SAAS6C,0BAA0BA,CAACC,IAAI,EAAE;MAC/C,IAAIC,MAAM,GAAG,IAAI;MACjB,IAAIC,WAAW,GAAG,IAAI,CAAC1E,KAAK;QAC1B2E,KAAK,GAAGD,WAAW,CAACC,KAAK;QACzBC,OAAO,GAAGF,WAAW,CAACE,OAAO;QAC7BC,WAAW,GAAGH,WAAW,CAACG,WAAW;QACrCC,SAAS,GAAGJ,WAAW,CAACI,SAAS;MACnC,IAAIC,SAAS,GAAGhC,WAAW,CAAC,IAAI,CAAC/C,KAAK,EAAE,KAAK,CAAC;MAC9C,OAAOwE,IAAI,IAAIA,IAAI,CAACQ,GAAG,CAAC,UAAUC,KAAK,EAAEnH,CAAC,EAAE;QAC1C,IAAIoH,QAAQ,GAAGpH,CAAC,KAAK+G,WAAW;QAChC,IAAIM,MAAM,GAAGD,QAAQ,GAAGJ,SAAS,GAAGH,KAAK;QACzC,IAAI3E,KAAK,GAAGX,aAAa,CAACA,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAE0F,SAAS,CAAC,EAAEE,KAAK,CAAC,EAAE,CAAC,CAAC,EAAE;UAChFC,QAAQ,EAAEA,QAAQ;UAClBC,MAAM,EAAEA,MAAM;UACdC,KAAK,EAAEtH,CAAC;UACR8G,OAAO,EAAEA,OAAO;UAChBN,gBAAgB,EAAEG,MAAM,CAACY,oBAAoB;UAC7CjB,cAAc,EAAEK,MAAM,CAACa;QACzB,CAAC,CAAC;QACF,OAAO,aAAapD,KAAK,CAACqD,aAAa,CAAC/C,KAAK,EAAEjE,QAAQ,CAAC;UACtDiH,SAAS,EAAE;QACb,CAAC,EAAEhC,kBAAkB,CAACiB,MAAM,CAACzE,KAAK,EAAEiF,KAAK,EAAEnH,CAAC,CAAC,EAAE;UAC7C;UACA;UACAD,GAAG,EAAE,YAAY,CAACqG,MAAM,CAACe,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,KAAK,CAACQ,CAAC,EAAE,GAAG,CAAC,CAACvB,MAAM,CAACe,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,KAAK,CAACS,CAAC,EAAE,GAAG,CAAC,CAACxB,MAAM,CAACe,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,KAAK,CAACvD,KAAK,EAAE,GAAG,CAAC,CAACwC,MAAM,CAACpG,CAAC;QACzO,CAAC,CAAC,EAAE,aAAaoE,KAAK,CAACqD,aAAa,CAAC9B,YAAY,EAAEzD,KAAK,CAAC,CAAC;MAC5D,CAAC,CAAC;IACJ;EACF,CAAC,EAAE;IACDnC,GAAG,EAAE,+BAA+B;IACpC6D,KAAK,EAAE,SAASiE,6BAA6BA,CAAA,EAAG;MAC9C,IAAIC,MAAM,GAAG,IAAI;MACjB,IAAIC,YAAY,GAAG,IAAI,CAAC7F,KAAK;QAC3BwE,IAAI,GAAGqB,YAAY,CAACrB,IAAI;QACxBsB,MAAM,GAAGD,YAAY,CAACC,MAAM;QAC5BC,iBAAiB,GAAGF,YAAY,CAACE,iBAAiB;QAClDC,cAAc,GAAGH,YAAY,CAACG,cAAc;QAC5CC,iBAAiB,GAAGJ,YAAY,CAACI,iBAAiB;QAClDC,eAAe,GAAGL,YAAY,CAACK,eAAe;QAC9CC,WAAW,GAAGN,YAAY,CAACM,WAAW;MACxC,IAAIC,QAAQ,GAAG,IAAI,CAACC,KAAK,CAACD,QAAQ;MAClC,OAAO,aAAalE,KAAK,CAACqD,aAAa,CAAClD,OAAO,EAAE;QAC/CiE,KAAK,EAAEN,cAAc;QACrBO,QAAQ,EAAEN,iBAAiB;QAC3Bf,QAAQ,EAAEa,iBAAiB;QAC3BS,MAAM,EAAEN,eAAe;QACvBO,IAAI,EAAE;UACJ1H,CAAC,EAAE;QACL,CAAC;QACD2H,EAAE,EAAE;UACF3H,CAAC,EAAE;QACL,CAAC;QACDlB,GAAG,EAAE,MAAM,CAACqG,MAAM,CAACiC,WAAW,CAAC;QAC/B/B,cAAc,EAAE,IAAI,CAACkB,kBAAkB;QACvChB,gBAAgB,EAAE,IAAI,CAACe;MACzB,CAAC,EAAE,UAAUsB,IAAI,EAAE;QACjB,IAAI5H,CAAC,GAAG4H,IAAI,CAAC5H,CAAC;QACd,IAAI6H,QAAQ,GAAGpC,IAAI,CAACQ,GAAG,CAAC,UAAUC,KAAK,EAAEG,KAAK,EAAE;UAC9C,IAAIyB,IAAI,GAAGT,QAAQ,IAAIA,QAAQ,CAAChB,KAAK,CAAC;UACtC,IAAIyB,IAAI,EAAE;YACR,IAAIC,aAAa,GAAGhE,iBAAiB,CAAC+D,IAAI,CAACpB,CAAC,EAAER,KAAK,CAACQ,CAAC,CAAC;YACtD,IAAIsB,aAAa,GAAGjE,iBAAiB,CAAC+D,IAAI,CAACnB,CAAC,EAAET,KAAK,CAACS,CAAC,CAAC;YACtD,IAAIsB,iBAAiB,GAAGlE,iBAAiB,CAAC+D,IAAI,CAACI,KAAK,EAAEhC,KAAK,CAACgC,KAAK,CAAC;YAClE,IAAIC,kBAAkB,GAAGpE,iBAAiB,CAAC+D,IAAI,CAACM,MAAM,EAAElC,KAAK,CAACkC,MAAM,CAAC;YACrE,OAAO9H,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAE4F,KAAK,CAAC,EAAE,CAAC,CAAC,EAAE;cACjDQ,CAAC,EAAEqB,aAAa,CAAC/H,CAAC,CAAC;cACnB2G,CAAC,EAAEqB,aAAa,CAAChI,CAAC,CAAC;cACnBkI,KAAK,EAAED,iBAAiB,CAACjI,CAAC,CAAC;cAC3BoI,MAAM,EAAED,kBAAkB,CAACnI,CAAC;YAC9B,CAAC,CAAC;UACJ;UACA,IAAI+G,MAAM,KAAK,YAAY,EAAE;YAC3B,IAAIsB,mBAAmB,GAAGtE,iBAAiB,CAAC,CAAC,EAAEmC,KAAK,CAACkC,MAAM,CAAC;YAC5D,IAAIE,CAAC,GAAGD,mBAAmB,CAACrI,CAAC,CAAC;YAC9B,OAAOM,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAE4F,KAAK,CAAC,EAAE,CAAC,CAAC,EAAE;cACjDS,CAAC,EAAET,KAAK,CAACS,CAAC,GAAGT,KAAK,CAACkC,MAAM,GAAGE,CAAC;cAC7BF,MAAM,EAAEE;YACV,CAAC,CAAC;UACJ;UACA,IAAIC,YAAY,GAAGxE,iBAAiB,CAAC,CAAC,EAAEmC,KAAK,CAACgC,KAAK,CAAC;UACpD,IAAIM,CAAC,GAAGD,YAAY,CAACvI,CAAC,CAAC;UACvB,OAAOM,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAE4F,KAAK,CAAC,EAAE,CAAC,CAAC,EAAE;YACjDgC,KAAK,EAAEM;UACT,CAAC,CAAC;QACJ,CAAC,CAAC;QACF,OAAO,aAAarF,KAAK,CAACqD,aAAa,CAAC/C,KAAK,EAAE,IAAI,EAAEoD,MAAM,CAACrB,0BAA0B,CAACqC,QAAQ,CAAC,CAAC;MACnG,CAAC,CAAC;IACJ;EACF,CAAC,EAAE;IACD/I,GAAG,EAAE,kBAAkB;IACvB6D,KAAK,EAAE,SAAS8F,gBAAgBA,CAAA,EAAG;MACjC,IAAIC,YAAY,GAAG,IAAI,CAACzH,KAAK;QAC3BwE,IAAI,GAAGiD,YAAY,CAACjD,IAAI;QACxBuB,iBAAiB,GAAG0B,YAAY,CAAC1B,iBAAiB;MACpD,IAAIK,QAAQ,GAAG,IAAI,CAACC,KAAK,CAACD,QAAQ;MAClC,IAAIL,iBAAiB,IAAIvB,IAAI,IAAIA,IAAI,CAACtG,MAAM,KAAK,CAACkI,QAAQ,IAAI,CAAC9D,OAAO,CAAC8D,QAAQ,EAAE5B,IAAI,CAAC,CAAC,EAAE;QACvF,OAAO,IAAI,CAACmB,6BAA6B,CAAC,CAAC;MAC7C;MACA,OAAO,IAAI,CAACpB,0BAA0B,CAACC,IAAI,CAAC;IAC9C;EACF,CAAC,EAAE;IACD3G,GAAG,EAAE,kBAAkB;IACvB6D,KAAK,EAAE,SAASgG,gBAAgBA,CAAA,EAAG;MACjC,IAAIC,MAAM,GAAG,IAAI;MACjB,IAAIC,YAAY,GAAG,IAAI,CAAC5H,KAAK;QAC3BwE,IAAI,GAAGoD,YAAY,CAACpD,IAAI;QACxBI,OAAO,GAAGgD,YAAY,CAAChD,OAAO;QAC9BC,WAAW,GAAG+C,YAAY,CAAC/C,WAAW;MACxC,IAAIgD,eAAe,GAAG9E,WAAW,CAAC,IAAI,CAAC/C,KAAK,CAAC8H,UAAU,EAAE,KAAK,CAAC;MAC/D,OAAOtD,IAAI,CAACQ,GAAG,CAAC,UAAUC,KAAK,EAAEnH,CAAC,EAAE;QAClC,IAAI4D,KAAK,GAAGuD,KAAK,CAACvD,KAAK;UACrBoG,UAAU,GAAG7C,KAAK,CAAC6C,UAAU;UAC7BC,IAAI,GAAGvK,wBAAwB,CAACyH,KAAK,EAAEjI,SAAS,CAAC;QACnD,IAAI,CAAC8K,UAAU,EAAE;UACf,OAAO,IAAI;QACb;QACA,IAAI9H,KAAK,GAAGX,aAAa,CAACA,aAAa,CAACA,aAAa,CAACA,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAE0I,IAAI,CAAC,EAAE,CAAC,CAAC,EAAE;UAC/FC,IAAI,EAAE;QACR,CAAC,EAAEF,UAAU,CAAC,EAAED,eAAe,CAAC,EAAErE,kBAAkB,CAACmE,MAAM,CAAC3H,KAAK,EAAEiF,KAAK,EAAEnH,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;UACjFwG,gBAAgB,EAAEqD,MAAM,CAACtC,oBAAoB;UAC7CjB,cAAc,EAAEuD,MAAM,CAACrC,kBAAkB;UACzCV,OAAO,EAAEA,OAAO;UAChBQ,KAAK,EAAEtH,CAAC;UACR0H,SAAS,EAAE;QACb,CAAC,CAAC;QACF,OAAO,aAAatD,KAAK,CAACqD,aAAa,CAAC9B,YAAY,EAAElF,QAAQ,CAAC;UAC7DV,GAAG,EAAE,iBAAiB,CAACqG,MAAM,CAACpG,CAAC,CAAC;UAChCqH,MAAM,EAAEwC,MAAM,CAAC3H,KAAK,CAAC8H,UAAU;UAC/B5C,QAAQ,EAAEpH,CAAC,KAAK+G;QAClB,CAAC,EAAE7E,KAAK,CAAC,CAAC;MACZ,CAAC,CAAC;IACJ;EACF,CAAC,EAAE;IACDnC,GAAG,EAAE,gBAAgB;IACrB6D,KAAK,EAAE,SAASuG,cAAcA,CAACC,QAAQ,EAAEC,UAAU,EAAE;MACnD,IAAI,IAAI,CAACnI,KAAK,CAAC+F,iBAAiB,IAAI,CAAC,IAAI,CAACM,KAAK,CAAClC,mBAAmB,EAAE;QACnE,OAAO,IAAI;MACb;MACA,IAAIiE,YAAY,GAAG,IAAI,CAACpI,KAAK;QAC3BwE,IAAI,GAAG4D,YAAY,CAAC5D,IAAI;QACxB6D,KAAK,GAAGD,YAAY,CAACC,KAAK;QAC1BC,KAAK,GAAGF,YAAY,CAACE,KAAK;QAC1BxC,MAAM,GAAGsC,YAAY,CAACtC,MAAM;QAC5ByC,QAAQ,GAAGH,YAAY,CAACG,QAAQ;MAClC,IAAIC,aAAa,GAAGxF,aAAa,CAACuF,QAAQ,EAAE9F,QAAQ,CAAC;MACrD,IAAI,CAAC+F,aAAa,EAAE;QAClB,OAAO,IAAI;MACb;MACA,IAAIC,MAAM,GAAG3C,MAAM,KAAK,UAAU,GAAGtB,IAAI,CAAC,CAAC,CAAC,CAAC2C,MAAM,GAAG,CAAC,GAAG3C,IAAI,CAAC,CAAC,CAAC,CAACyC,KAAK,GAAG,CAAC;MAC3E,IAAIyB,kBAAkB,GAAG,SAASA,kBAAkBA,CAACC,SAAS,EAAE/D,OAAO,EAAE;QACvE;AACR;AACA;AACA;QACQ,IAAIlD,KAAK,GAAGsC,KAAK,CAAC4E,OAAO,CAACD,SAAS,CAACjH,KAAK,CAAC,GAAGiH,SAAS,CAACjH,KAAK,CAAC,CAAC,CAAC,GAAGiH,SAAS,CAACjH,KAAK;QACjF,OAAO;UACL+D,CAAC,EAAEkD,SAAS,CAAClD,CAAC;UACdC,CAAC,EAAEiD,SAAS,CAACjD,CAAC;UACdhE,KAAK,EAAEA,KAAK;UACZmH,QAAQ,EAAE1F,iBAAiB,CAACwF,SAAS,EAAE/D,OAAO;QAChD,CAAC;MACH,CAAC;MACD,IAAIkE,aAAa,GAAG;QAClBC,QAAQ,EAAEb,QAAQ,GAAG,gBAAgB,CAAChE,MAAM,CAACiE,UAAU,EAAE,GAAG,CAAC,GAAG;MAClE,CAAC;MACD,OAAO,aAAajG,KAAK,CAACqD,aAAa,CAAC/C,KAAK,EAAEsG,aAAa,EAAEN,aAAa,CAACxD,GAAG,CAAC,UAAUgE,IAAI,EAAE;QAC9F,OAAO,aAAa9G,KAAK,CAAC+G,YAAY,CAACD,IAAI,EAAE;UAC3CnL,GAAG,EAAE,YAAY,CAACqG,MAAM,CAACiE,UAAU,EAAE,GAAG,CAAC,CAACjE,MAAM,CAAC8E,IAAI,CAAChJ,KAAK,CAAC4E,OAAO,CAAC;UACpEJ,IAAI,EAAEA,IAAI;UACV6D,KAAK,EAAEA,KAAK;UACZC,KAAK,EAAEA,KAAK;UACZxC,MAAM,EAAEA,MAAM;UACd2C,MAAM,EAAEA,MAAM;UACdC,kBAAkB,EAAEA;QACtB,CAAC,CAAC;MACJ,CAAC,CAAC,CAAC;IACL;EACF,CAAC,EAAE;IACD7K,GAAG,EAAE,QAAQ;IACb6D,KAAK,EAAE,SAASwH,MAAMA,CAAA,EAAG;MACvB,IAAIC,YAAY,GAAG,IAAI,CAACnJ,KAAK;QAC3BoJ,IAAI,GAAGD,YAAY,CAACC,IAAI;QACxB5E,IAAI,GAAG2E,YAAY,CAAC3E,IAAI;QACxBgB,SAAS,GAAG2D,YAAY,CAAC3D,SAAS;QAClC6C,KAAK,GAAGc,YAAY,CAACd,KAAK;QAC1BC,KAAK,GAAGa,YAAY,CAACb,KAAK;QAC1Be,IAAI,GAAGF,YAAY,CAACE,IAAI;QACxBC,GAAG,GAAGH,YAAY,CAACG,GAAG;QACtBrC,KAAK,GAAGkC,YAAY,CAAClC,KAAK;QAC1BE,MAAM,GAAGgC,YAAY,CAAChC,MAAM;QAC5BpB,iBAAiB,GAAGoD,YAAY,CAACpD,iBAAiB;QAClD+B,UAAU,GAAGqB,YAAY,CAACrB,UAAU;QACpCyB,EAAE,GAAGJ,YAAY,CAACI,EAAE;MACtB,IAAIH,IAAI,IAAI,CAAC5E,IAAI,IAAI,CAACA,IAAI,CAACtG,MAAM,EAAE;QACjC,OAAO,IAAI;MACb;MACA,IAAIiG,mBAAmB,GAAG,IAAI,CAACkC,KAAK,CAAClC,mBAAmB;MACxD,IAAIqF,UAAU,GAAGpH,IAAI,CAAC,cAAc,EAAEoD,SAAS,CAAC;MAChD,IAAIiE,SAAS,GAAGpB,KAAK,IAAIA,KAAK,CAACqB,iBAAiB;MAChD,IAAIC,SAAS,GAAGrB,KAAK,IAAIA,KAAK,CAACoB,iBAAiB;MAChD,IAAIxB,QAAQ,GAAGuB,SAAS,IAAIE,SAAS;MACrC,IAAIxB,UAAU,GAAG5F,KAAK,CAACgH,EAAE,CAAC,GAAG,IAAI,CAACA,EAAE,GAAGA,EAAE;MACzC,OAAO,aAAarH,KAAK,CAACqD,aAAa,CAAC/C,KAAK,EAAE;QAC7CgD,SAAS,EAAEgE;MACb,CAAC,EAAEC,SAAS,IAAIE,SAAS,GAAG,aAAazH,KAAK,CAACqD,aAAa,CAAC,MAAM,EAAE,IAAI,EAAE,aAAarD,KAAK,CAACqD,aAAa,CAAC,UAAU,EAAE;QACtHgE,EAAE,EAAE,WAAW,CAACrF,MAAM,CAACiE,UAAU;MACnC,CAAC,EAAE,aAAajG,KAAK,CAACqD,aAAa,CAAC,MAAM,EAAE;QAC1CE,CAAC,EAAEgE,SAAS,GAAGJ,IAAI,GAAGA,IAAI,GAAGpC,KAAK,GAAG,CAAC;QACtCvB,CAAC,EAAEiE,SAAS,GAAGL,GAAG,GAAGA,GAAG,GAAGnC,MAAM,GAAG,CAAC;QACrCF,KAAK,EAAEwC,SAAS,GAAGxC,KAAK,GAAGA,KAAK,GAAG,CAAC;QACpCE,MAAM,EAAEwC,SAAS,GAAGxC,MAAM,GAAGA,MAAM,GAAG;MACxC,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI,EAAE,aAAajF,KAAK,CAACqD,aAAa,CAAC/C,KAAK,EAAE;QACnDgD,SAAS,EAAE,yBAAyB;QACpCuD,QAAQ,EAAEb,QAAQ,GAAG,gBAAgB,CAAChE,MAAM,CAACiE,UAAU,EAAE,GAAG,CAAC,GAAG;MAClE,CAAC,EAAEL,UAAU,GAAG,IAAI,CAACJ,gBAAgB,CAAC,CAAC,GAAG,IAAI,EAAE,IAAI,CAACF,gBAAgB,CAAC,CAAC,CAAC,EAAE,IAAI,CAACS,cAAc,CAACC,QAAQ,EAAEC,UAAU,CAAC,EAAE,CAAC,CAACpC,iBAAiB,IAAI5B,mBAAmB,KAAKxB,SAAS,CAACiH,kBAAkB,CAAC,IAAI,CAAC5J,KAAK,EAAEwE,IAAI,CAAC,CAAC;IACrN;EACF,CAAC,CAAC,EAAE,CAAC;IACH3G,GAAG,EAAE,0BAA0B;IAC/B6D,KAAK,EAAE,SAASmI,wBAAwBA,CAACC,SAAS,EAAEC,SAAS,EAAE;MAC7D,IAAID,SAAS,CAAC3D,WAAW,KAAK4D,SAAS,CAACC,eAAe,EAAE;QACvD,OAAO;UACLA,eAAe,EAAEF,SAAS,CAAC3D,WAAW;UACtC8D,OAAO,EAAEH,SAAS,CAACtF,IAAI;UACvB4B,QAAQ,EAAE2D,SAAS,CAACE;QACtB,CAAC;MACH;MACA,IAAIH,SAAS,CAACtF,IAAI,KAAKuF,SAAS,CAACE,OAAO,EAAE;QACxC,OAAO;UACLA,OAAO,EAAEH,SAAS,CAACtF;QACrB,CAAC;MACH;MACA,OAAO,IAAI;IACb;EACF,CAAC,CAAC,CAAC;AACL,CAAC,CAACrC,aAAa,CAAC;AAChBlF,IAAI,GAAG0G,GAAG;AACVpE,eAAe,CAACoE,GAAG,EAAE,aAAa,EAAE,KAAK,CAAC;AAC1CpE,eAAe,CAACoE,GAAG,EAAE,cAAc,EAAE;EACnCuG,OAAO,EAAE,CAAC;EACVC,OAAO,EAAE,CAAC;EACVC,UAAU,EAAE,MAAM;EAClBC,YAAY,EAAE,CAAC;EACfjB,IAAI,EAAE,KAAK;EACX5E,IAAI,EAAE,EAAE;EACRsB,MAAM,EAAE,UAAU;EAClBhB,SAAS,EAAE,KAAK;EAChBiB,iBAAiB,EAAE,CAAC9C,MAAM,CAACqH,KAAK;EAChCtE,cAAc,EAAE,CAAC;EACjBC,iBAAiB,EAAE,GAAG;EACtBC,eAAe,EAAE;AACnB,CAAC,CAAC;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA3G,eAAe,CAACoE,GAAG,EAAE,iBAAiB,EAAE,UAAU4G,KAAK,EAAE;EACvD,IAAIvK,KAAK,GAAGuK,KAAK,CAACvK,KAAK;IACrBgJ,IAAI,GAAGuB,KAAK,CAACvB,IAAI;IACjBwB,WAAW,GAAGD,KAAK,CAACC,WAAW;IAC/BC,QAAQ,GAAGF,KAAK,CAACE,QAAQ;IACzBpC,KAAK,GAAGkC,KAAK,CAAClC,KAAK;IACnBC,KAAK,GAAGiC,KAAK,CAACjC,KAAK;IACnBoC,UAAU,GAAGH,KAAK,CAACG,UAAU;IAC7BC,UAAU,GAAGJ,KAAK,CAACI,UAAU;IAC7BC,WAAW,GAAGL,KAAK,CAACK,WAAW;IAC/BC,cAAc,GAAGN,KAAK,CAACM,cAAc;IACrCC,aAAa,GAAGP,KAAK,CAACO,aAAa;IACnCrC,MAAM,GAAG8B,KAAK,CAAC9B,MAAM;EACvB,IAAIsC,GAAG,GAAGzH,iBAAiB,CAACkH,WAAW,EAAExB,IAAI,CAAC;EAC9C,IAAI,CAAC+B,GAAG,EAAE;IACR,OAAO,IAAI;EACb;EACA,IAAIjF,MAAM,GAAG9F,KAAK,CAAC8F,MAAM;EACzB,IAAIkF,gBAAgB,GAAGhC,IAAI,CAACiC,IAAI,CAACC,YAAY;EAC7C,IAAIC,SAAS,GAAGH,gBAAgB,KAAKI,SAAS,GAAG/L,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAE2L,gBAAgB,CAAC,EAAEhC,IAAI,CAAChJ,KAAK,CAAC,GAAGgJ,IAAI,CAAChJ,KAAK;EAC5H,IAAI4E,OAAO,GAAGuG,SAAS,CAACvG,OAAO;IAC7B2D,QAAQ,GAAG4C,SAAS,CAAC5C,QAAQ;IAC7B8C,gBAAgB,GAAGF,SAAS,CAACd,YAAY;EAC3C,IAAIiB,WAAW,GAAGxF,MAAM,KAAK,YAAY,GAAGwC,KAAK,GAAGD,KAAK;EACzD,IAAIkD,aAAa,GAAGX,WAAW,GAAGU,WAAW,CAACE,KAAK,CAACC,MAAM,CAAC,CAAC,GAAG,IAAI;EACnE,IAAIC,SAAS,GAAGrI,iBAAiB,CAAC;IAChCiI,WAAW,EAAEA;EACf,CAAC,CAAC;EACF,IAAIK,KAAK,GAAG3I,aAAa,CAACuF,QAAQ,EAAE7F,IAAI,CAAC;EACzC,IAAIkJ,KAAK,GAAGd,aAAa,CAAC9F,GAAG,CAAC,UAAUC,KAAK,EAAEG,KAAK,EAAE;IACpD,IAAI1D,KAAK,EAAE+D,CAAC,EAAEC,CAAC,EAAEuB,KAAK,EAAEE,MAAM,EAAEW,UAAU;IAC1C,IAAI8C,WAAW,EAAE;MACflJ,KAAK,GAAG0B,gBAAgB,CAACwH,WAAW,CAACC,cAAc,GAAGzF,KAAK,CAAC,EAAEmG,aAAa,CAAC;IAC9E,CAAC,MAAM;MACL7J,KAAK,GAAGyB,iBAAiB,CAAC8B,KAAK,EAAEL,OAAO,CAAC;MACzC,IAAI,CAACZ,KAAK,CAAC4E,OAAO,CAAClH,KAAK,CAAC,EAAE;QACzBA,KAAK,GAAG,CAACgK,SAAS,EAAEhK,KAAK,CAAC;MAC5B;IACF;IACA,IAAI2I,YAAY,GAAG3G,oBAAoB,CAAC2H,gBAAgB,EAAEpO,IAAI,CAACiO,YAAY,CAACb,YAAY,CAAC,CAAC3I,KAAK,CAAC,CAAC,CAAC,EAAE0D,KAAK,CAAC;IAC1G,IAAIU,MAAM,KAAK,YAAY,EAAE;MAC3B,IAAI+F,KAAK;MACT,IAAIC,KAAK,GAAG,CAACxD,KAAK,CAACkD,KAAK,CAAC9J,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE4G,KAAK,CAACkD,KAAK,CAAC9J,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;QACxDqK,cAAc,GAAGD,KAAK,CAAC,CAAC,CAAC;QACzBE,iBAAiB,GAAGF,KAAK,CAAC,CAAC,CAAC;MAC9BrG,CAAC,GAAGvC,sBAAsB,CAAC;QACzB+I,IAAI,EAAE5D,KAAK;QACX6D,KAAK,EAAExB,UAAU;QACjBD,QAAQ,EAAEA,QAAQ;QAClBhC,MAAM,EAAEsC,GAAG,CAACtC,MAAM;QAClBxD,KAAK,EAAEA,KAAK;QACZG,KAAK,EAAEA;MACT,CAAC,CAAC;MACFM,CAAC,GAAG,CAACmG,KAAK,GAAGG,iBAAiB,KAAK,IAAI,IAAIA,iBAAiB,KAAK,KAAK,CAAC,GAAGA,iBAAiB,GAAGD,cAAc,MAAM,IAAI,IAAIF,KAAK,KAAK,KAAK,CAAC,GAAGA,KAAK,GAAGT,SAAS;MAC9JnE,KAAK,GAAG8D,GAAG,CAACoB,IAAI;MAChB,IAAIC,cAAc,GAAGL,cAAc,GAAGC,iBAAiB;MACvD7E,MAAM,GAAGlF,MAAM,CAACoK,KAAK,CAACD,cAAc,CAAC,GAAG,CAAC,GAAGA,cAAc;MAC1DtE,UAAU,GAAG;QACXrC,CAAC,EAAEA,CAAC;QACJC,CAAC,EAAE4C,KAAK,CAAC5C,CAAC;QACVuB,KAAK,EAAEA,KAAK;QACZE,MAAM,EAAEmB,KAAK,CAACnB;MAChB,CAAC;MACD,IAAImF,IAAI,CAACC,GAAG,CAAClC,YAAY,CAAC,GAAG,CAAC,IAAIiC,IAAI,CAACC,GAAG,CAACpF,MAAM,CAAC,GAAGmF,IAAI,CAACC,GAAG,CAAClC,YAAY,CAAC,EAAE;QAC3E,IAAImC,KAAK,GAAG3J,QAAQ,CAACsE,MAAM,IAAIkD,YAAY,CAAC,IAAIiC,IAAI,CAACC,GAAG,CAAClC,YAAY,CAAC,GAAGiC,IAAI,CAACC,GAAG,CAACpF,MAAM,CAAC,CAAC;QAC1FzB,CAAC,IAAI8G,KAAK;QACVrF,MAAM,IAAIqF,KAAK;MACjB;IACF,CAAC,MAAM;MACL,IAAIC,KAAK,GAAG,CAACpE,KAAK,CAACmD,KAAK,CAAC9J,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE2G,KAAK,CAACmD,KAAK,CAAC9J,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;QACxDgL,eAAe,GAAGD,KAAK,CAAC,CAAC,CAAC;QAC1BE,kBAAkB,GAAGF,KAAK,CAAC,CAAC,CAAC;MAC/BhH,CAAC,GAAGiH,eAAe;MACnBhH,CAAC,GAAGxC,sBAAsB,CAAC;QACzB+I,IAAI,EAAE3D,KAAK;QACX4D,KAAK,EAAEvB,UAAU;QACjBF,QAAQ,EAAEA,QAAQ;QAClBhC,MAAM,EAAEsC,GAAG,CAACtC,MAAM;QAClBxD,KAAK,EAAEA,KAAK;QACZG,KAAK,EAAEA;MACT,CAAC,CAAC;MACF6B,KAAK,GAAG0F,kBAAkB,GAAGD,eAAe;MAC5CvF,MAAM,GAAG4D,GAAG,CAACoB,IAAI;MACjBrE,UAAU,GAAG;QACXrC,CAAC,EAAE4C,KAAK,CAAC5C,CAAC;QACVC,CAAC,EAAEA,CAAC;QACJuB,KAAK,EAAEoB,KAAK,CAACpB,KAAK;QAClBE,MAAM,EAAEA;MACV,CAAC;MACD,IAAImF,IAAI,CAACC,GAAG,CAAClC,YAAY,CAAC,GAAG,CAAC,IAAIiC,IAAI,CAACC,GAAG,CAACtF,KAAK,CAAC,GAAGqF,IAAI,CAACC,GAAG,CAAClC,YAAY,CAAC,EAAE;QAC1E,IAAIuC,MAAM,GAAG/J,QAAQ,CAACoE,KAAK,IAAIoD,YAAY,CAAC,IAAIiC,IAAI,CAACC,GAAG,CAAClC,YAAY,CAAC,GAAGiC,IAAI,CAACC,GAAG,CAACtF,KAAK,CAAC,CAAC;QACzFA,KAAK,IAAI2F,MAAM;MACjB;IACF;IACA,OAAOvN,aAAa,CAACA,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAE4F,KAAK,CAAC,EAAE,CAAC,CAAC,EAAE;MAC/DQ,CAAC,EAAEA,CAAC;MACJC,CAAC,EAAEA,CAAC;MACJuB,KAAK,EAAEA,KAAK;MACZE,MAAM,EAAEA,MAAM;MACdzF,KAAK,EAAEkJ,WAAW,GAAGlJ,KAAK,GAAGA,KAAK,CAAC,CAAC,CAAC;MACrCmL,OAAO,EAAE5H,KAAK;MACd6C,UAAU,EAAEA;IACd,CAAC,EAAE6D,KAAK,IAAIA,KAAK,CAACvG,KAAK,CAAC,IAAIuG,KAAK,CAACvG,KAAK,CAAC,CAACpF,KAAK,CAAC,EAAE,CAAC,CAAC,EAAE;MACnD8M,cAAc,EAAE,CAACvJ,cAAc,CAACyF,IAAI,EAAE/D,KAAK,CAAC,CAAC;MAC7C8H,eAAe,EAAE;QACftH,CAAC,EAAEA,CAAC,GAAGwB,KAAK,GAAG,CAAC;QAChBvB,CAAC,EAAEA,CAAC,GAAGyB,MAAM,GAAG;MAClB;IACF,CAAC,CAAC;EACJ,CAAC,CAAC;EACF,OAAO9H,aAAa,CAAC;IACnBmF,IAAI,EAAEoH,KAAK;IACX9F,MAAM,EAAEA;EACV,CAAC,EAAE2C,MAAM,CAAC;AACZ,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
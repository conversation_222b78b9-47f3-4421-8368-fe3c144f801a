{"ast": null, "code": "/**\n * @license lucide-react v0.294.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst BatteryCharging = createLucideIcon(\"BatteryCharging\", [[\"path\", {\n  d: \"M15 7h1a2 2 0 0 1 2 2v6a2 2 0 0 1-2 2h-2\",\n  key: \"1sdynx\"\n}], [\"path\", {\n  d: \"M6 7H4a2 2 0 0 0-2 2v6a2 2 0 0 0 2 2h1\",\n  key: \"1gkd3k\"\n}], [\"path\", {\n  d: \"m11 7-3 5h4l-3 5\",\n  key: \"b4a64w\"\n}], [\"line\", {\n  x1: \"22\",\n  x2: \"22\",\n  y1: \"11\",\n  y2: \"13\",\n  key: \"4dh1rd\"\n}]]);\nexport { BatteryCharging as default };", "map": {"version": 3, "names": ["BatteryCharging", "createLucideIcon", "d", "key", "x1", "x2", "y1", "y2"], "sources": ["C:\\Users\\<USER>\\OneDrive\\Desktop\\MVP\\client\\node_modules\\lucide-react\\src\\icons\\battery-charging.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name BatteryCharging\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTUgN2gxYTIgMiAwIDAgMSAyIDJ2NmEyIDIgMCAwIDEtMiAyaC0yIiAvPgogIDxwYXRoIGQ9Ik02IDdINGEyIDIgMCAwIDAtMiAydjZhMiAyIDAgMCAwIDIgMmgxIiAvPgogIDxwYXRoIGQ9Im0xMSA3LTMgNWg0bC0zIDUiIC8+CiAgPGxpbmUgeDE9IjIyIiB4Mj0iMjIiIHkxPSIxMSIgeTI9IjEzIiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/battery-charging\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst BatteryCharging = createLucideIcon('BatteryCharging', [\n  ['path', { d: 'M15 7h1a2 2 0 0 1 2 2v6a2 2 0 0 1-2 2h-2', key: '1sdynx' }],\n  ['path', { d: 'M6 7H4a2 2 0 0 0-2 2v6a2 2 0 0 0 2 2h1', key: '1gkd3k' }],\n  ['path', { d: 'm11 7-3 5h4l-3 5', key: 'b4a64w' }],\n  ['line', { x1: '22', x2: '22', y1: '11', y2: '13', key: '4dh1rd' }],\n]);\n\nexport default BatteryCharging;\n"], "mappings": ";;;;;;;;AAaM,MAAAA,eAAA,GAAkBC,gBAAA,CAAiB,iBAAmB,GAC1D,CAAC,MAAQ;EAAEC,CAAA,EAAG,0CAA4C;EAAAC,GAAA,EAAK;AAAA,CAAU,GACzE,CAAC,MAAQ;EAAED,CAAA,EAAG,wCAA0C;EAAAC,GAAA,EAAK;AAAA,CAAU,GACvE,CAAC,MAAQ;EAAED,CAAA,EAAG,kBAAoB;EAAAC,GAAA,EAAK;AAAA,CAAU,GACjD,CAAC,QAAQ;EAAEC,EAAA,EAAI,IAAM;EAAAC,EAAA,EAAI,IAAM;EAAAC,EAAA,EAAI,IAAM;EAAAC,EAAA,EAAI,IAAM;EAAAJ,GAAA,EAAK;AAAA,CAAU,EACnE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
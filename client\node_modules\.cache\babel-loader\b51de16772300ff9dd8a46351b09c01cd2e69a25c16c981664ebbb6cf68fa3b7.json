{"ast": null, "code": "/**\n * @license lucide-react v0.294.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst Caravan = createLucideIcon(\"Caravan\", [[\"rect\", {\n  width: \"4\",\n  height: \"4\",\n  x: \"2\",\n  y: \"9\",\n  key: \"1vcvhd\"\n}], [\"rect\", {\n  width: \"4\",\n  height: \"10\",\n  x: \"10\",\n  y: \"9\",\n  key: \"1b7ev2\"\n}], [\"path\", {\n  d: \"M18 19V9a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v8a2 2 0 0 0 2 2h2\",\n  key: \"19jm3t\"\n}], [\"circle\", {\n  cx: \"8\",\n  cy: \"19\",\n  r: \"2\",\n  key: \"t8fc5s\"\n}], [\"path\", {\n  d: \"M10 19h12v-2\",\n  key: \"1yu2qx\"\n}]]);\nexport { Caravan as default };", "map": {"version": 3, "names": ["Caravan", "createLucideIcon", "width", "height", "x", "y", "key", "d", "cx", "cy", "r"], "sources": ["C:\\Users\\<USER>\\OneDrive\\Desktop\\MVP\\client\\node_modules\\lucide-react\\src\\icons\\caravan.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name Caravan\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cmVjdCB3aWR0aD0iNCIgaGVpZ2h0PSI0IiB4PSIyIiB5PSI5IiAvPgogIDxyZWN0IHdpZHRoPSI0IiBoZWlnaHQ9IjEwIiB4PSIxMCIgeT0iOSIgLz4KICA8cGF0aCBkPSJNMTggMTlWOWE0IDQgMCAwIDAtNC00SDZhNCA0IDAgMCAwLTQgNHY4YTIgMiAwIDAgMCAyIDJoMiIgLz4KICA8Y2lyY2xlIGN4PSI4IiBjeT0iMTkiIHI9IjIiIC8+CiAgPHBhdGggZD0iTTEwIDE5aDEydi0yIiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/caravan\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Caravan = createLucideIcon('Caravan', [\n  ['rect', { width: '4', height: '4', x: '2', y: '9', key: '1vcvhd' }],\n  ['rect', { width: '4', height: '10', x: '10', y: '9', key: '1b7ev2' }],\n  ['path', { d: 'M18 19V9a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v8a2 2 0 0 0 2 2h2', key: '19jm3t' }],\n  ['circle', { cx: '8', cy: '19', r: '2', key: 't8fc5s' }],\n  ['path', { d: 'M10 19h12v-2', key: '1yu2qx' }],\n]);\n\nexport default Caravan;\n"], "mappings": ";;;;;;;;AAaM,MAAAA,OAAA,GAAUC,gBAAA,CAAiB,SAAW,GAC1C,CAAC,QAAQ;EAAEC,KAAA,EAAO,GAAK;EAAAC,MAAA,EAAQ,GAAK;EAAAC,CAAA,EAAG,GAAK;EAAAC,CAAA,EAAG,GAAK;EAAAC,GAAA,EAAK;AAAA,CAAU,GACnE,CAAC,QAAQ;EAAEJ,KAAA,EAAO,GAAK;EAAAC,MAAA,EAAQ,IAAM;EAAAC,CAAA,EAAG,IAAM;EAAAC,CAAA,EAAG,GAAK;EAAAC,GAAA,EAAK;AAAA,CAAU,GACrE,CAAC,MAAQ;EAAEC,CAAA,EAAG,0DAA4D;EAAAD,GAAA,EAAK;AAAA,CAAU,GACzF,CAAC,QAAU;EAAEE,EAAI;EAAKC,EAAI;EAAMC,CAAG;EAAKJ,GAAK;AAAA,CAAU,GACvD,CAAC,MAAQ;EAAEC,CAAA,EAAG,cAAgB;EAAAD,GAAA,EAAK;AAAA,CAAU,EAC9C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
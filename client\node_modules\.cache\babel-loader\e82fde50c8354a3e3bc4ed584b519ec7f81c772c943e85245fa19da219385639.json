{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MVP\\\\client\\\\src\\\\App.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport Dashboard from './components/Dashboard';\nimport AnalysisForm from './components/AnalysisForm';\nimport { Mic, BarChart3 } from 'lucide-react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction App() {\n  _s();\n  const [analysisResults, setAnalysisResults] = useState(null);\n  const [isAnalyzing, setIsAnalyzing] = useState(false);\n  const handleAnalysisComplete = results => {\n    setAnalysisResults(results);\n    setIsAnalyzing(false);\n  };\n  const handleAnalysisStart = () => {\n    setIsAnalyzing(true);\n    setAnalysisResults(null);\n  };\n  const resetAnalysis = () => {\n    setAnalysisResults(null);\n    setIsAnalyzing(false);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen bg-gray-50\",\n    children: [/*#__PURE__*/_jsxDEV(\"header\", {\n      className: \"bg-white shadow-sm border-b\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"container mx-auto px-4 py-4\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center justify-between\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center gap-3\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"p-2 bg-blue-100 rounded-lg\",\n              children: /*#__PURE__*/_jsxDEV(Mic, {\n                className: \"w-6 h-6 text-blue-600\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 33,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 32,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n                className: \"text-2xl font-bold text-gray-900\",\n                children: \"Voice Bot QA Dashboard\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 36,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-gray-600\",\n                children: \"Analyze call quality, silence, repetition & conversational flow\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 37,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 35,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 31,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center gap-2 text-sm text-gray-500\",\n            children: [/*#__PURE__*/_jsxDEV(BarChart3, {\n              className: \"w-4 h-4\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 41,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: \"Real-time Analysis\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 42,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 40,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 30,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 29,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 28,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"container mx-auto px-4 py-8\",\n      children: [!analysisResults && !isAnalyzing && /*#__PURE__*/_jsxDEV(AnalysisForm, {\n        onAnalysisStart: handleAnalysisStart,\n        onAnalysisComplete: handleAnalysisComplete\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 50,\n        columnNumber: 11\n      }, this), isAnalyzing && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"card text-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"loading\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"spinner\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 59,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 58,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-lg font-semibold mb-2\",\n          children: \"Analyzing Call...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 61,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-600\",\n          children: \"Processing audio and transcript for comprehensive QA analysis\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 62,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 57,\n        columnNumber: 11\n      }, this), analysisResults && /*#__PURE__*/_jsxDEV(Dashboard, {\n        results: analysisResults,\n        onReset: resetAnalysis\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 67,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 48,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"footer\", {\n      className: \"bg-white border-t mt-12\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"container mx-auto px-4 py-6\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center text-gray-600\",\n          children: /*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"Voice Bot QA Analysis System - Powered by OpenAI & Advanced Audio Processing\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 78,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 77,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 76,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 75,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 26,\n    columnNumber: 5\n  }, this);\n}\n_s(App, \"lP7H71ND5bG1oOzmOUezAm4k7p0=\");\n_c = App;\nexport default App;\nvar _c;\n$RefreshReg$(_c, \"App\");", "map": {"version": 3, "names": ["React", "useState", "Dashboard", "AnalysisForm", "Mic", "BarChart3", "jsxDEV", "_jsxDEV", "App", "_s", "analysisResults", "setAnalysisResults", "isAnalyzing", "setIsAnalyzing", "handleAnalysisComplete", "results", "handleAnalysisStart", "resetAnalysis", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onAnalysisStart", "onAnalysisComplete", "onReset", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/MVP/client/src/App.js"], "sourcesContent": ["import React, { useState } from 'react';\r\nimport Dashboard from './components/Dashboard';\r\nimport AnalysisForm from './components/AnalysisForm';\r\nimport { Mic, BarChart3 } from 'lucide-react';\r\n\r\nfunction App() {\r\n  const [analysisResults, setAnalysisResults] = useState(null);\r\n  const [isAnalyzing, setIsAnalyzing] = useState(false);\r\n\r\n  const handleAnalysisComplete = (results) => {\r\n    setAnalysisResults(results);\r\n    setIsAnalyzing(false);\r\n  };\r\n\r\n  const handleAnalysisStart = () => {\r\n    setIsAnalyzing(true);\r\n    setAnalysisResults(null);\r\n  };\r\n\r\n  const resetAnalysis = () => {\r\n    setAnalysisResults(null);\r\n    setIsAnalyzing(false);\r\n  };\r\n\r\n  return (\r\n    <div className=\"min-h-screen bg-gray-50\">\r\n      {/* Header */}\r\n      <header className=\"bg-white shadow-sm border-b\">\r\n        <div className=\"container mx-auto px-4 py-4\">\r\n          <div className=\"flex items-center justify-between\">\r\n            <div className=\"flex items-center gap-3\">\r\n              <div className=\"p-2 bg-blue-100 rounded-lg\">\r\n                <Mic className=\"w-6 h-6 text-blue-600\" />\r\n              </div>\r\n              <div>\r\n                <h1 className=\"text-2xl font-bold text-gray-900\">Voice Bot QA Dashboard</h1>\r\n                <p className=\"text-gray-600\">Analyze call quality, silence, repetition & conversational flow</p>\r\n              </div>\r\n            </div>\r\n            <div className=\"flex items-center gap-2 text-sm text-gray-500\">\r\n              <BarChart3 className=\"w-4 h-4\" />\r\n              <span>Real-time Analysis</span>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </header>\r\n\r\n      <div className=\"container mx-auto px-4 py-8\">\r\n        {!analysisResults && !isAnalyzing && (\r\n          <AnalysisForm \r\n            onAnalysisStart={handleAnalysisStart}\r\n            onAnalysisComplete={handleAnalysisComplete}\r\n          />\r\n        )}\r\n\r\n        {isAnalyzing && (\r\n          <div className=\"card text-center\">\r\n            <div className=\"loading\">\r\n              <div className=\"spinner\"></div>\r\n            </div>\r\n            <h3 className=\"text-lg font-semibold mb-2\">Analyzing Call...</h3>\r\n            <p className=\"text-gray-600\">Processing audio and transcript for comprehensive QA analysis</p>\r\n          </div>\r\n        )}\r\n\r\n        {analysisResults && (\r\n          <Dashboard \r\n            results={analysisResults}\r\n            onReset={resetAnalysis}\r\n          />\r\n        )}\r\n      </div>\r\n\r\n      {/* Footer */}\r\n      <footer className=\"bg-white border-t mt-12\">\r\n        <div className=\"container mx-auto px-4 py-6\">\r\n          <div className=\"text-center text-gray-600\">\r\n            <p>Voice Bot QA Analysis System - Powered by OpenAI & Advanced Audio Processing</p>\r\n          </div>\r\n        </div>\r\n      </footer>\r\n    </div>\r\n  );\r\n}\r\n\r\nexport default App;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,OAAOC,SAAS,MAAM,wBAAwB;AAC9C,OAAOC,YAAY,MAAM,2BAA2B;AACpD,SAASC,GAAG,EAAEC,SAAS,QAAQ,cAAc;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE9C,SAASC,GAAGA,CAAA,EAAG;EAAAC,EAAA;EACb,MAAM,CAACC,eAAe,EAAEC,kBAAkB,CAAC,GAAGV,QAAQ,CAAC,IAAI,CAAC;EAC5D,MAAM,CAACW,WAAW,EAAEC,cAAc,CAAC,GAAGZ,QAAQ,CAAC,KAAK,CAAC;EAErD,MAAMa,sBAAsB,GAAIC,OAAO,IAAK;IAC1CJ,kBAAkB,CAACI,OAAO,CAAC;IAC3BF,cAAc,CAAC,KAAK,CAAC;EACvB,CAAC;EAED,MAAMG,mBAAmB,GAAGA,CAAA,KAAM;IAChCH,cAAc,CAAC,IAAI,CAAC;IACpBF,kBAAkB,CAAC,IAAI,CAAC;EAC1B,CAAC;EAED,MAAMM,aAAa,GAAGA,CAAA,KAAM;IAC1BN,kBAAkB,CAAC,IAAI,CAAC;IACxBE,cAAc,CAAC,KAAK,CAAC;EACvB,CAAC;EAED,oBACEN,OAAA;IAAKW,SAAS,EAAC,yBAAyB;IAAAC,QAAA,gBAEtCZ,OAAA;MAAQW,SAAS,EAAC,6BAA6B;MAAAC,QAAA,eAC7CZ,OAAA;QAAKW,SAAS,EAAC,6BAA6B;QAAAC,QAAA,eAC1CZ,OAAA;UAAKW,SAAS,EAAC,mCAAmC;UAAAC,QAAA,gBAChDZ,OAAA;YAAKW,SAAS,EAAC,yBAAyB;YAAAC,QAAA,gBACtCZ,OAAA;cAAKW,SAAS,EAAC,4BAA4B;cAAAC,QAAA,eACzCZ,OAAA,CAACH,GAAG;gBAACc,SAAS,EAAC;cAAuB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtC,CAAC,eACNhB,OAAA;cAAAY,QAAA,gBACEZ,OAAA;gBAAIW,SAAS,EAAC,kCAAkC;gBAAAC,QAAA,EAAC;cAAsB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC5EhB,OAAA;gBAAGW,SAAS,EAAC,eAAe;gBAAAC,QAAA,EAAC;cAA+D;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7F,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACNhB,OAAA;YAAKW,SAAS,EAAC,+CAA+C;YAAAC,QAAA,gBAC5DZ,OAAA,CAACF,SAAS;cAACa,SAAS,EAAC;YAAS;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACjChB,OAAA;cAAAY,QAAA,EAAM;YAAkB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5B,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC,eAEThB,OAAA;MAAKW,SAAS,EAAC,6BAA6B;MAAAC,QAAA,GACzC,CAACT,eAAe,IAAI,CAACE,WAAW,iBAC/BL,OAAA,CAACJ,YAAY;QACXqB,eAAe,EAAER,mBAAoB;QACrCS,kBAAkB,EAAEX;MAAuB;QAAAM,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5C,CACF,EAEAX,WAAW,iBACVL,OAAA;QAAKW,SAAS,EAAC,kBAAkB;QAAAC,QAAA,gBAC/BZ,OAAA;UAAKW,SAAS,EAAC,SAAS;UAAAC,QAAA,eACtBZ,OAAA;YAAKW,SAAS,EAAC;UAAS;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5B,CAAC,eACNhB,OAAA;UAAIW,SAAS,EAAC,4BAA4B;UAAAC,QAAA,EAAC;QAAiB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACjEhB,OAAA;UAAGW,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAC;QAA6D;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC3F,CACN,EAEAb,eAAe,iBACdH,OAAA,CAACL,SAAS;QACRa,OAAO,EAAEL,eAAgB;QACzBgB,OAAO,EAAET;MAAc;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACxB,CACF;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,eAGNhB,OAAA;MAAQW,SAAS,EAAC,yBAAyB;MAAAC,QAAA,eACzCZ,OAAA;QAAKW,SAAS,EAAC,6BAA6B;QAAAC,QAAA,eAC1CZ,OAAA;UAAKW,SAAS,EAAC,2BAA2B;UAAAC,QAAA,eACxCZ,OAAA;YAAAY,QAAA,EAAG;UAA4E;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChF;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACN,CAAC;AAEV;AAACd,EAAA,CA9EQD,GAAG;AAAAmB,EAAA,GAAHnB,GAAG;AAgFZ,eAAeA,GAAG;AAAC,IAAAmB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
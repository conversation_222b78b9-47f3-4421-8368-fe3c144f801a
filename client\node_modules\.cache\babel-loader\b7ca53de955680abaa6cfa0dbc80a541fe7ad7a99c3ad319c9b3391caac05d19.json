{"ast": null, "code": "/**\n * @license lucide-react v0.294.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst CloudSnow = createLucideIcon(\"CloudSnow\", [[\"path\", {\n  d: \"M4 14.899A7 7 0 1 1 15.71 8h1.79a4.5 4.5 0 0 1 2.5 8.242\",\n  key: \"1pljnt\"\n}], [\"path\", {\n  d: \"M8 15h.01\",\n  key: \"a7atzg\"\n}], [\"path\", {\n  d: \"M8 19h.01\",\n  key: \"puxtts\"\n}], [\"path\", {\n  d: \"M12 17h.01\",\n  key: \"p32p05\"\n}], [\"path\", {\n  d: \"M12 21h.01\",\n  key: \"h35vbk\"\n}], [\"path\", {\n  d: \"M16 15h.01\",\n  key: \"rnfrdf\"\n}], [\"path\", {\n  d: \"M16 19h.01\",\n  key: \"1vcnzz\"\n}]]);\nexport { CloudSnow as default };", "map": {"version": 3, "names": ["CloudSnow", "createLucideIcon", "d", "key"], "sources": ["C:\\Users\\<USER>\\OneDrive\\Desktop\\MVP\\client\\node_modules\\lucide-react\\src\\icons\\cloud-snow.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name CloudSnow\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNNCAxNC44OTlBNyA3IDAgMSAxIDE1LjcxIDhoMS43OWE0LjUgNC41IDAgMCAxIDIuNSA4LjI0MiIgLz4KICA8cGF0aCBkPSJNOCAxNWguMDEiIC8+CiAgPHBhdGggZD0iTTggMTloLjAxIiAvPgogIDxwYXRoIGQ9Ik0xMiAxN2guMDEiIC8+CiAgPHBhdGggZD0iTTEyIDIxaC4wMSIgLz4KICA8cGF0aCBkPSJNMTYgMTVoLjAxIiAvPgogIDxwYXRoIGQ9Ik0xNiAxOWguMDEiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/cloud-snow\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst CloudSnow = createLucideIcon('CloudSnow', [\n  ['path', { d: 'M4 14.899A7 7 0 1 1 15.71 8h1.79a4.5 4.5 0 0 1 2.5 8.242', key: '1pljnt' }],\n  ['path', { d: 'M8 15h.01', key: 'a7atzg' }],\n  ['path', { d: 'M8 19h.01', key: 'puxtts' }],\n  ['path', { d: 'M12 17h.01', key: 'p32p05' }],\n  ['path', { d: 'M12 21h.01', key: 'h35vbk' }],\n  ['path', { d: 'M16 15h.01', key: 'rnfrdf' }],\n  ['path', { d: 'M16 19h.01', key: '1vcnzz' }],\n]);\n\nexport default CloudSnow;\n"], "mappings": ";;;;;;;;AAaM,MAAAA,SAAA,GAAYC,gBAAA,CAAiB,WAAa,GAC9C,CAAC,MAAQ;EAAEC,CAAA,EAAG,0DAA4D;EAAAC,GAAA,EAAK;AAAA,CAAU,GACzF,CAAC,MAAQ;EAAED,CAAA,EAAG,WAAa;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC1C,CAAC,MAAQ;EAAED,CAAA,EAAG,WAAa;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC1C,CAAC,MAAQ;EAAED,CAAA,EAAG,YAAc;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC3C,CAAC,MAAQ;EAAED,CAAA,EAAG,YAAc;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC3C,CAAC,MAAQ;EAAED,CAAA,EAAG,YAAc;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC3C,CAAC,MAAQ;EAAED,CAAA,EAAG,YAAc;EAAAC,GAAA,EAAK;AAAA,CAAU,EAC5C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
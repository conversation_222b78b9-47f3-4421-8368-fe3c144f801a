{"ast": null, "code": "/**\n * @license lucide-react v0.294.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst BookLock = createLucideIcon(\"BookLock\", [[\"path\", {\n  d: \"M4 19.5v-15A2.5 2.5 0 0 1 6.5 2H10\",\n  key: \"18wgow\"\n}], [\"path\", {\n  d: \"M20 15v7H6.5a2.5 2.5 0 0 1 0-5H20\",\n  key: \"dpch1j\"\n}], [\"rect\", {\n  width: \"8\",\n  height: \"5\",\n  x: \"12\",\n  y: \"6\",\n  rx: \"1\",\n  key: \"9nqwug\"\n}], [\"path\", {\n  d: \"M18 6V4a2 2 0 1 0-4 0v2\",\n  key: \"1aquzs\"\n}]]);\nexport { BookLock as default };", "map": {"version": 3, "names": ["BookLock", "createLucideIcon", "d", "key", "width", "height", "x", "y", "rx"], "sources": ["C:\\Users\\<USER>\\OneDrive\\Desktop\\MVP\\client\\node_modules\\lucide-react\\src\\icons\\book-lock.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name BookLock\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNNCAxOS41di0xNUEyLjUgMi41IDAgMCAxIDYuNSAySDEwIiAvPgogIDxwYXRoIGQ9Ik0yMCAxNXY3SDYuNWEyLjUgMi41IDAgMCAxIDAtNUgyMCIgLz4KICA8cmVjdCB3aWR0aD0iOCIgaGVpZ2h0PSI1IiB4PSIxMiIgeT0iNiIgcng9IjEiIC8+CiAgPHBhdGggZD0iTTE4IDZWNGEyIDIgMCAxIDAtNCAwdjIiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/book-lock\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst BookLock = createLucideIcon('BookLock', [\n  ['path', { d: 'M4 19.5v-15A2.5 2.5 0 0 1 6.5 2H10', key: '18wgow' }],\n  ['path', { d: 'M20 15v7H6.5a2.5 2.5 0 0 1 0-5H20', key: 'dpch1j' }],\n  ['rect', { width: '8', height: '5', x: '12', y: '6', rx: '1', key: '9nqwug' }],\n  ['path', { d: 'M18 6V4a2 2 0 1 0-4 0v2', key: '1aquzs' }],\n]);\n\nexport default BookLock;\n"], "mappings": ";;;;;;;;AAaM,MAAAA,QAAA,GAAWC,gBAAA,CAAiB,UAAY,GAC5C,CAAC,MAAQ;EAAEC,CAAA,EAAG,oCAAsC;EAAAC,GAAA,EAAK;AAAA,CAAU,GACnE,CAAC,MAAQ;EAAED,CAAA,EAAG,mCAAqC;EAAAC,GAAA,EAAK;AAAA,CAAU,GAClE,CAAC,QAAQ;EAAEC,KAAA,EAAO;EAAKC,MAAQ;EAAKC,CAAG;EAAMC,CAAA,EAAG,GAAK;EAAAC,EAAA,EAAI,GAAK;EAAAL,GAAA,EAAK;AAAA,CAAU,GAC7E,CAAC,MAAQ;EAAED,CAAA,EAAG,yBAA2B;EAAAC,GAAA,EAAK;AAAA,CAAU,EACzD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
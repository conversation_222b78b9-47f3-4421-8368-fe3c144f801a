{"ast": null, "code": "/**\n * @license lucide-react v0.294.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst Landmark = createLucideIcon(\"Landmark\", [[\"line\", {\n  x1: \"3\",\n  x2: \"21\",\n  y1: \"22\",\n  y2: \"22\",\n  key: \"j8o0r\"\n}], [\"line\", {\n  x1: \"6\",\n  x2: \"6\",\n  y1: \"18\",\n  y2: \"11\",\n  key: \"10tf0k\"\n}], [\"line\", {\n  x1: \"10\",\n  x2: \"10\",\n  y1: \"18\",\n  y2: \"11\",\n  key: \"54lgf6\"\n}], [\"line\", {\n  x1: \"14\",\n  x2: \"14\",\n  y1: \"18\",\n  y2: \"11\",\n  key: \"380y\"\n}], [\"line\", {\n  x1: \"18\",\n  x2: \"18\",\n  y1: \"18\",\n  y2: \"11\",\n  key: \"1kevvc\"\n}], [\"polygon\", {\n  points: \"12 2 20 7 4 7\",\n  key: \"jkujk7\"\n}]]);\nexport { Landmark as default };", "map": {"version": 3, "names": ["Landmark", "createLucideIcon", "x1", "x2", "y1", "y2", "key", "points"], "sources": ["C:\\Users\\<USER>\\OneDrive\\Desktop\\MVP\\client\\node_modules\\lucide-react\\src\\icons\\landmark.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name Landmark\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8bGluZSB4MT0iMyIgeDI9IjIxIiB5MT0iMjIiIHkyPSIyMiIgLz4KICA8bGluZSB4MT0iNiIgeDI9IjYiIHkxPSIxOCIgeTI9IjExIiAvPgogIDxsaW5lIHgxPSIxMCIgeDI9IjEwIiB5MT0iMTgiIHkyPSIxMSIgLz4KICA8bGluZSB4MT0iMTQiIHgyPSIxNCIgeTE9IjE4IiB5Mj0iMTEiIC8+CiAgPGxpbmUgeDE9IjE4IiB4Mj0iMTgiIHkxPSIxOCIgeTI9IjExIiAvPgogIDxwb2x5Z29uIHBvaW50cz0iMTIgMiAyMCA3IDQgNyIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/landmark\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Landmark = createLucideIcon('Landmark', [\n  ['line', { x1: '3', x2: '21', y1: '22', y2: '22', key: 'j8o0r' }],\n  ['line', { x1: '6', x2: '6', y1: '18', y2: '11', key: '10tf0k' }],\n  ['line', { x1: '10', x2: '10', y1: '18', y2: '11', key: '54lgf6' }],\n  ['line', { x1: '14', x2: '14', y1: '18', y2: '11', key: '380y' }],\n  ['line', { x1: '18', x2: '18', y1: '18', y2: '11', key: '1kevvc' }],\n  ['polygon', { points: '12 2 20 7 4 7', key: 'jkujk7' }],\n]);\n\nexport default Landmark;\n"], "mappings": ";;;;;;;;AAaM,MAAAA,QAAA,GAAWC,gBAAA,CAAiB,UAAY,GAC5C,CAAC,QAAQ;EAAEC,EAAA,EAAI,GAAK;EAAAC,EAAA,EAAI,IAAM;EAAAC,EAAA,EAAI,IAAM;EAAAC,EAAA,EAAI,IAAM;EAAAC,GAAA,EAAK;AAAA,CAAS,GAChE,CAAC,QAAQ;EAAEJ,EAAA,EAAI,GAAK;EAAAC,EAAA,EAAI,GAAK;EAAAC,EAAA,EAAI,IAAM;EAAAC,EAAA,EAAI,IAAM;EAAAC,GAAA,EAAK;AAAA,CAAU,GAChE,CAAC,QAAQ;EAAEJ,EAAA,EAAI,IAAM;EAAAC,EAAA,EAAI,IAAM;EAAAC,EAAA,EAAI,IAAM;EAAAC,EAAA,EAAI,IAAM;EAAAC,GAAA,EAAK;AAAA,CAAU,GAClE,CAAC,QAAQ;EAAEJ,EAAA,EAAI,IAAM;EAAAC,EAAA,EAAI,IAAM;EAAAC,EAAA,EAAI,IAAM;EAAAC,EAAA,EAAI,IAAM;EAAAC,GAAA,EAAK;AAAA,CAAQ,GAChE,CAAC,QAAQ;EAAEJ,EAAA,EAAI,IAAM;EAAAC,EAAA,EAAI,IAAM;EAAAC,EAAA,EAAI,IAAM;EAAAC,EAAA,EAAI,IAAM;EAAAC,GAAA,EAAK;AAAA,CAAU,GAClE,CAAC,SAAW;EAAEC,MAAA,EAAQ,eAAiB;EAAAD,GAAA,EAAK;AAAA,CAAU,EACvD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "/**\n * The base implementation of `_.sortBy` which uses `comparer` to define the\n * sort order of `array` and replaces criteria objects with their corresponding\n * values.\n *\n * @private\n * @param {Array} array The array to sort.\n * @param {Function} comparer The function to define sort order.\n * @returns {Array} Returns `array`.\n */\nfunction baseSortBy(array, comparer) {\n  var length = array.length;\n  array.sort(comparer);\n  while (length--) {\n    array[length] = array[length].value;\n  }\n  return array;\n}\nmodule.exports = baseSortBy;", "map": {"version": 3, "names": ["baseSortBy", "array", "comparer", "length", "sort", "value", "module", "exports"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/MVP/client/node_modules/lodash/_baseSortBy.js"], "sourcesContent": ["/**\n * The base implementation of `_.sortBy` which uses `comparer` to define the\n * sort order of `array` and replaces criteria objects with their corresponding\n * values.\n *\n * @private\n * @param {Array} array The array to sort.\n * @param {Function} comparer The function to define sort order.\n * @returns {Array} Returns `array`.\n */\nfunction baseSortBy(array, comparer) {\n  var length = array.length;\n\n  array.sort(comparer);\n  while (length--) {\n    array[length] = array[length].value;\n  }\n  return array;\n}\n\nmodule.exports = baseSortBy;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,UAAUA,CAACC,KAAK,EAAEC,QAAQ,EAAE;EACnC,IAAIC,MAAM,GAAGF,KAAK,CAACE,MAAM;EAEzBF,KAAK,CAACG,IAAI,CAACF,QAAQ,CAAC;EACpB,OAAOC,MAAM,EAAE,EAAE;IACfF,KAAK,CAACE,MAAM,CAAC,GAAGF,KAAK,CAACE,MAAM,CAAC,CAACE,KAAK;EACrC;EACA,OAAOJ,KAAK;AACd;AAEAK,MAAM,CAACC,OAAO,GAAGP,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}
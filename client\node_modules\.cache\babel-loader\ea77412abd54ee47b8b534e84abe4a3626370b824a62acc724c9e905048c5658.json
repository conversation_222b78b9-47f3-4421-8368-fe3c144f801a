{"ast": null, "code": "/**\n * @license lucide-react v0.294.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst PictureInPicture2 = createLucideIcon(\"PictureInPicture2\", [[\"path\", {\n  d: \"M21 9V6a2 2 0 0 0-2-2H4a2 2 0 0 0-2 2v10c0 1.1.9 2 2 2h4\",\n  key: \"daa4of\"\n}], [\"rect\", {\n  width: \"10\",\n  height: \"7\",\n  x: \"12\",\n  y: \"13\",\n  rx: \"2\",\n  key: \"1nb8gs\"\n}]]);\nexport { PictureInPicture2 as default };", "map": {"version": 3, "names": ["PictureInPicture2", "createLucideIcon", "d", "key", "width", "height", "x", "y", "rx"], "sources": ["C:\\Users\\<USER>\\OneDrive\\Desktop\\MVP\\client\\node_modules\\lucide-react\\src\\icons\\picture-in-picture-2.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name PictureInPicture2\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMjEgOVY2YTIgMiAwIDAgMC0yLTJINGEyIDIgMCAwIDAtMiAydjEwYzAgMS4xLjkgMiAyIDJoNCIgLz4KICA8cmVjdCB3aWR0aD0iMTAiIGhlaWdodD0iNyIgeD0iMTIiIHk9IjEzIiByeD0iMiIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/picture-in-picture-2\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst PictureInPicture2 = createLucideIcon('PictureInPicture2', [\n  ['path', { d: 'M21 9V6a2 2 0 0 0-2-2H4a2 2 0 0 0-2 2v10c0 1.1.9 2 2 2h4', key: 'daa4of' }],\n  ['rect', { width: '10', height: '7', x: '12', y: '13', rx: '2', key: '1nb8gs' }],\n]);\n\nexport default PictureInPicture2;\n"], "mappings": ";;;;;;;;AAaM,MAAAA,iBAAA,GAAoBC,gBAAA,CAAiB,mBAAqB,GAC9D,CAAC,MAAQ;EAAEC,CAAA,EAAG,0DAA4D;EAAAC,GAAA,EAAK;AAAA,CAAU,GACzF,CAAC,QAAQ;EAAEC,KAAA,EAAO;EAAMC,MAAQ;EAAKC,CAAG;EAAMC,CAAA,EAAG,IAAM;EAAAC,EAAA,EAAI,GAAK;EAAAL,GAAA,EAAK;AAAA,CAAU,EAChF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
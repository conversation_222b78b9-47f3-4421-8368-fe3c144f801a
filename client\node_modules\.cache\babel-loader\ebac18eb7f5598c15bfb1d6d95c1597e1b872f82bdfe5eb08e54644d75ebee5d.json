{"ast": null, "code": "/**\n * @license lucide-react v0.294.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst Award = createLucideIcon(\"Award\", [[\"circle\", {\n  cx: \"12\",\n  cy: \"8\",\n  r: \"6\",\n  key: \"1vp47v\"\n}], [\"path\", {\n  d: \"M15.477 12.89 17 22l-5-3-5 3 1.523-9.11\",\n  key: \"em7aur\"\n}]]);\nexport { Award as default };", "map": {"version": 3, "names": ["Award", "createLucideIcon", "cx", "cy", "r", "key", "d"], "sources": ["C:\\Users\\<USER>\\OneDrive\\Desktop\\MVP\\client\\node_modules\\lucide-react\\src\\icons\\award.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name Award\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8Y2lyY2xlIGN4PSIxMiIgY3k9IjgiIHI9IjYiIC8+CiAgPHBhdGggZD0iTTE1LjQ3NyAxMi44OSAxNyAyMmwtNS0zLTUgMyAxLjUyMy05LjExIiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/award\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Award = createLucideIcon('Award', [\n  ['circle', { cx: '12', cy: '8', r: '6', key: '1vp47v' }],\n  ['path', { d: 'M15.477 12.89 17 22l-5-3-5 3 1.523-9.11', key: 'em7aur' }],\n]);\n\nexport default Award;\n"], "mappings": ";;;;;;;;AAaM,MAAAA,KAAA,GAAQC,gBAAA,CAAiB,OAAS,GACtC,CAAC,QAAU;EAAEC,EAAI;EAAMC,EAAI;EAAKC,CAAG;EAAKC,GAAK;AAAA,CAAU,GACvD,CAAC,MAAQ;EAAEC,CAAA,EAAG,yCAA2C;EAAAD,GAAA,EAAK;AAAA,CAAU,EACzE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
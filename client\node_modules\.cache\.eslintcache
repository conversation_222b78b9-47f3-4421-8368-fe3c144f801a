[{"C:\\Users\\<USER>\\OneDrive\\Desktop\\MVP\\client\\src\\index.js": "1", "C:\\Users\\<USER>\\OneDrive\\Desktop\\MVP\\client\\src\\App.js": "2", "C:\\Users\\<USER>\\OneDrive\\Desktop\\MVP\\client\\src\\components\\Dashboard.js": "3", "C:\\Users\\<USER>\\OneDrive\\Desktop\\MVP\\client\\src\\components\\AnalysisForm.js": "4", "C:\\Users\\<USER>\\OneDrive\\Desktop\\MVP\\client\\src\\components\\WaveformChart.js": "5", "C:\\Users\\<USER>\\OneDrive\\Desktop\\MVP\\client\\src\\components\\ScoreOverview.js": "6", "C:\\Users\\<USER>\\OneDrive\\Desktop\\MVP\\client\\src\\components\\IntentFlow.js": "7", "C:\\Users\\<USER>\\OneDrive\\Desktop\\MVP\\client\\src\\components\\DetailedResults.js": "8", "C:\\Users\\<USER>\\OneDrive\\Desktop\\MVP\\client\\src\\components\\MetricsGrid.js": "9"}, {"size": 263, "mtime": 1757396002109, "results": "10", "hashOfConfig": "11"}, {"size": 2838, "mtime": 1757396053390, "results": "12", "hashOfConfig": "11"}, {"size": 2739, "mtime": 1757396123262, "results": "13", "hashOfConfig": "11"}, {"size": 10362, "mtime": 1757401901239, "results": "14", "hashOfConfig": "11"}, {"size": 5235, "mtime": 1757396187147, "results": "15", "hashOfConfig": "11"}, {"size": 3079, "mtime": 1757396142995, "results": "16", "hashOfConfig": "11"}, {"size": 7819, "mtime": 1757396221377, "results": "17", "hashOfConfig": "11"}, {"size": 13568, "mtime": 1757396270338, "results": "18", "hashOfConfig": "11"}, {"size": 3429, "mtime": 1757396161620, "results": "19", "hashOfConfig": "11"}, {"filePath": "20", "messages": "21", "suppressedMessages": "22", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "hozuv9", {"filePath": "23", "messages": "24", "suppressedMessages": "25", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "26", "messages": "27", "suppressedMessages": "28", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "29", "messages": "30", "suppressedMessages": "31", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "32", "messages": "33", "suppressedMessages": "34", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "35", "messages": "36", "suppressedMessages": "37", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "38", "messages": "39", "suppressedMessages": "40", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "41", "messages": "42", "suppressedMessages": "43", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "44", "messages": "45", "suppressedMessages": "46", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\OneDrive\\Desktop\\MVP\\client\\src\\index.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\MVP\\client\\src\\App.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\MVP\\client\\src\\components\\Dashboard.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\MVP\\client\\src\\components\\AnalysisForm.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\MVP\\client\\src\\components\\WaveformChart.js", ["47", "48"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\MVP\\client\\src\\components\\ScoreOverview.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\MVP\\client\\src\\components\\IntentFlow.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\MVP\\client\\src\\components\\DetailedResults.js", ["49", "50"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\MVP\\client\\src\\components\\MetricsGrid.js", [], [], {"ruleId": "51", "severity": 1, "message": "52", "line": 2, "column": 10, "nodeType": "53", "messageId": "54", "endLine": 2, "endColumn": 19}, {"ruleId": "51", "severity": 1, "message": "55", "line": 2, "column": 101, "nodeType": "53", "messageId": "54", "endLine": 2, "endColumn": 105}, {"ruleId": "51", "severity": 1, "message": "56", "line": 2, "column": 50, "nodeType": "53", "messageId": "54", "endLine": 2, "endColumn": 61}, {"ruleId": "51", "severity": 1, "message": "57", "line": 2, "column": 63, "nodeType": "53", "messageId": "54", "endLine": 2, "endColumn": 72}, "no-unused-vars", "'LineChart' is defined but never used.", "Identifier", "unusedVar", "'Area' is defined but never used.", "'ChevronDown' is defined but never used.", "'ChevronUp' is defined but never used."]
{"ast": null, "code": "/**\n * @license lucide-react v0.294.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst IterationCcw = createLucideIcon(\"IterationCcw\", [[\"path\", {\n  d: \"M20 10c0-4.4-3.6-8-8-8s-8 3.6-8 8 3.6 8 8 8h8\",\n  key: \"4znkd0\"\n}], [\"polyline\", {\n  points: \"16 14 20 18 16 22\",\n  key: \"11njsm\"\n}]]);\nexport { IterationCcw as default };", "map": {"version": 3, "names": ["IterationCcw", "createLucideIcon", "d", "key", "points"], "sources": ["C:\\Users\\<USER>\\OneDrive\\Desktop\\MVP\\client\\node_modules\\lucide-react\\src\\icons\\iteration-ccw.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name IterationCcw\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMjAgMTBjMC00LjQtMy42LTgtOC04cy04IDMuNi04IDggMy42IDggOCA4aDgiIC8+CiAgPHBvbHlsaW5lIHBvaW50cz0iMTYgMTQgMjAgMTggMTYgMjIiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/iteration-ccw\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst IterationCcw = createLucideIcon('IterationCcw', [\n  ['path', { d: 'M20 10c0-4.4-3.6-8-8-8s-8 3.6-8 8 3.6 8 8 8h8', key: '4znkd0' }],\n  ['polyline', { points: '16 14 20 18 16 22', key: '11njsm' }],\n]);\n\nexport default IterationCcw;\n"], "mappings": ";;;;;;;;AAaM,MAAAA,YAAA,GAAeC,gBAAA,CAAiB,cAAgB,GACpD,CAAC,MAAQ;EAAEC,CAAA,EAAG,+CAAiD;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC9E,CAAC,UAAY;EAAEC,MAAA,EAAQ,mBAAqB;EAAAD,GAAA,EAAK;AAAA,CAAU,EAC5D", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
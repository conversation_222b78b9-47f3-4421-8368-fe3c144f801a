{"ast": null, "code": "/**\n * @license lucide-react v0.294.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst Repeat2 = createLucideIcon(\"Repeat2\", [[\"path\", {\n  d: \"m2 9 3-3 3 3\",\n  key: \"1ltn5i\"\n}], [\"path\", {\n  d: \"M13 18H7a2 2 0 0 1-2-2V6\",\n  key: \"1r6tfw\"\n}], [\"path\", {\n  d: \"m22 15-3 3-3-3\",\n  key: \"4rnwn2\"\n}], [\"path\", {\n  d: \"M11 6h6a2 2 0 0 1 2 2v10\",\n  key: \"2f72bc\"\n}]]);\nexport { Repeat2 as default };", "map": {"version": 3, "names": ["Repeat2", "createLucideIcon", "d", "key"], "sources": ["C:\\Users\\<USER>\\OneDrive\\Desktop\\MVP\\client\\node_modules\\lucide-react\\src\\icons\\repeat-2.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name Repeat2\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJtMiA5IDMtMyAzIDMiIC8+CiAgPHBhdGggZD0iTTEzIDE4SDdhMiAyIDAgMCAxLTItMlY2IiAvPgogIDxwYXRoIGQ9Im0yMiAxNS0zIDMtMy0zIiAvPgogIDxwYXRoIGQ9Ik0xMSA2aDZhMiAyIDAgMCAxIDIgMnYxMCIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/repeat-2\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Repeat2 = createLucideIcon('Repeat2', [\n  ['path', { d: 'm2 9 3-3 3 3', key: '1ltn5i' }],\n  ['path', { d: 'M13 18H7a2 2 0 0 1-2-2V6', key: '1r6tfw' }],\n  ['path', { d: 'm22 15-3 3-3-3', key: '4rnwn2' }],\n  ['path', { d: 'M11 6h6a2 2 0 0 1 2 2v10', key: '2f72bc' }],\n]);\n\nexport default Repeat2;\n"], "mappings": ";;;;;;;;AAaM,MAAAA,OAAA,GAAUC,gBAAA,CAAiB,SAAW,GAC1C,CAAC,MAAQ;EAAEC,CAAA,EAAG,cAAgB;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC7C,CAAC,MAAQ;EAAED,CAAA,EAAG,0BAA4B;EAAAC,GAAA,EAAK;AAAA,CAAU,GACzD,CAAC,MAAQ;EAAED,CAAA,EAAG,gBAAkB;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC/C,CAAC,MAAQ;EAAED,CAAA,EAAG,0BAA4B;EAAAC,GAAA,EAAK;AAAA,CAAU,EAC1D", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
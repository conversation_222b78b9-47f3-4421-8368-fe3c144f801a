// Custom Call QA Analyzer - No OpenAI dependency
class CallQAAnalyzer {
    constructor() {
        // Initialize custom intent patterns and conversation flow rules
        this.initializeIntentPatterns();
    }

    initializeIntentPatterns() {
        // Enhanced regex patterns for intent detection
        this.intentPatterns = {
            greeting: {
                patterns: [
                    /\b(hello|hi|good\s+(morning|afternoon|evening)|welcome|thank\s+you\s+for\s+calling)\b/gi,
                    /\bhow\s+(can|may)\s+i\s+help\s+you\b/gi,
                    /\bwhat\s+can\s+i\s+do\s+for\s+you\b/gi
                ],
                score: 10,
                required: true
            },
            problem_identification: {
                patterns: [
                    /\b(problem|issue|trouble|difficulty|error|bug|not\s+working)\b/gi,
                    /\bi\s+(need|want|require)\s+help\s+with\b/gi,
                    /\bcan\s+you\s+help\s+me\s+with\b/gi,
                    /\bi\s+(can't|cannot|unable\s+to)\b/gi
                ],
                score: 15,
                required: true
            },
            information_gathering: {
                patterns: [
                    /\b(can\s+you\s+provide|please\s+provide|what\s+is\s+your|may\s+i\s+have)\b/gi,
                    /\b(email|phone|account|name|address|id|number)\b/gi,
                    /\blet\s+me\s+(check|verify|look\s+into)\b/gi,
                    /\bi\s+need\s+to\s+(verify|confirm|check)\b/gi
                ],
                score: 12,
                required: true
            },
            solution_offering: {
                patterns: [
                    /\bi\s+can\s+(help|fix|resolve|solve)\b/gi,
                    /\blet\s+me\s+(fix|resolve|solve|help)\b/gi,
                    /\bhere's\s+what\s+we\s+can\s+do\b/gi,
                    /\bi'll\s+(take\s+care\s+of|handle|fix)\b/gi
                ],
                score: 15,
                required: true
            },
            confirmation: {
                patterns: [
                    /\b(does\s+that\s+work|is\s+that\s+okay|does\s+that\s+help)\b/gi,
                    /\b(can\s+you\s+confirm|please\s+confirm|is\s+that\s+correct)\b/gi,
                    /\b(are\s+you\s+able\s+to|can\s+you\s+try)\b/gi
                ],
                score: 8,
                required: false
            },
            closing: {
                patterns: [
                    /\b(anything\s+else|is\s+there\s+anything\s+else)\b/gi,
                    /\b(thank\s+you|thanks|have\s+a\s+great\s+day)\b/gi,
                    /\b(you're\s+welcome|glad\s+i\s+could\s+help)\b/gi,
                    /\b(goodbye|bye|take\s+care)\b/gi
                ],
                score: 10,
                required: true
            }
        };

        // Conversation flow expectations
        this.expectedFlow = [
            'greeting',
            'problem_identification',
            'information_gathering',
            'solution_offering',
            'confirmation',
            'closing'
        ];

        // Customer satisfaction indicators
        this.satisfactionPatterns = {
            positive: [
                /\b(thank\s+you|thanks|great|perfect|excellent|wonderful)\b/gi,
                /\b(that\s+works|that's\s+perfect|exactly\s+what\s+i\s+needed)\b/gi,
                /\b(you've\s+been\s+very\s+helpful|really\s+appreciate)\b/gi
            ],
            negative: [
                /\b(frustrated|annoyed|disappointed|unhappy|terrible)\b/gi,
                /\b(this\s+is\s+ridiculous|waste\s+of\s+time|not\s+helpful)\b/gi,
                /\b(i\s+want\s+to\s+speak\s+to\s+manager|escalate)\b/gi
            ]
        };
    }

    async analyzeCall(transcript, audioData, config) {
        console.log('🚀 Starting comprehensive call analysis...');
        
        try {
            // Perform all analyses
            const silenceSegments = this.detectSilenceSegments(audioData, config);
            const repetitions = this.detectRepetitions(transcript, config);
            const latencyAnalysis = this.analyzeCallLatency(audioData, config);
            const intentFlow = await this.detectIntents(transcript);
            
            const { overallScore, scoreBreakdown } = this.calculateWeightedScore(
                silenceSegments, repetitions, latencyAnalysis, intentFlow
            );
            
            const visualizationData = this.generateVisualizationData(audioData, silenceSegments);
            
            console.log('✅ Analysis completed successfully!');
            
            return {
                overallScore,
                callDuration: audioData ? audioData.duration : 0,
                silenceViolations: silenceSegments,
                repetitions,
                intentFlow,
                latencyAnalysis,
                scoreBreakdown,
                visualizationData
            };
            
        } catch (error) {
            console.error('❌ Error during analysis:', error);
            throw error;
        }
    }

    detectSilenceSegments(audioData, config) {
        console.log('🔇 Detecting silence segments...');
        
        if (!audioData) {
            // Create mock silence segments for demonstration
            return [
                {
                    startTime: 45.2,
                    endTime: 51.8,
                    duration: 6.6,
                    speaker: 'bot'
                },
                {
                    startTime: 89.1,
                    endTime: 95.3,
                    duration: 6.2,
                    speaker: 'bot'
                }
            ];
        }

        const silenceSegments = [];
        const threshold = config.silenceThreshold;
        
        // Simple silence detection logic
        // In a real implementation, you'd use audio processing libraries
        if (audioData.silences) {
            audioData.silences.forEach((silence, index) => {
                if (silence.duration >= threshold) {
                    silenceSegments.push({
                        startTime: silence.start,
                        endTime: silence.end,
                        duration: silence.duration,
                        speaker: index % 2 === 0 ? 'bot' : 'human'
                    });
                }
            });
        }

        console.log(`✅ Found ${silenceSegments.length} silence segments`);
        return silenceSegments;
    }

    detectRepetitions(transcript, config) {
        console.log('🔄 Detecting repetitions...');
        
        const repetitions = [];
        const botTurns = this.extractBotTurns(transcript);
        
        for (let i = 0; i < botTurns.length; i++) {
            for (let j = i + 1; j < botTurns.length; j++) {
                const similarity = this.calculateSimilarity(botTurns[i].text, botTurns[j].text);
                
                if (similarity > config.repetitionSimilarityThreshold) {
                    repetitions.push({
                        turn1: botTurns[i].turnNumber,
                        turn2: botTurns[j].turnNumber,
                        similarityScore: similarity,
                        text1: botTurns[i].text,
                        text2: botTurns[j].text
                    });
                }
            }
        }
        
        console.log(`✅ Found ${repetitions.length} repetitions`);
        return repetitions;
    }

    extractBotTurns(transcript) {
        const botTurns = [];
        const lines = transcript.split('\n');
        let turnNumber = 0;
        
        lines.forEach(line => {
            line = line.trim();
            if (line.startsWith('Chat Bot:') || line.startsWith('Bot:')) {
                turnNumber++;
                const text = line.includes(':') ? line.split(':', 2)[1].trim() : line;
                botTurns.push({
                    turnNumber,
                    text
                });
            }
        });
        
        return botTurns;
    }

    calculateSimilarity(text1, text2) {
        // Simple similarity calculation using Levenshtein distance
        const matrix = [];
        const len1 = text1.length;
        const len2 = text2.length;

        for (let i = 0; i <= len2; i++) {
            matrix[i] = [i];
        }

        for (let j = 0; j <= len1; j++) {
            matrix[0][j] = j;
        }

        for (let i = 1; i <= len2; i++) {
            for (let j = 1; j <= len1; j++) {
                if (text2.charAt(i - 1) === text1.charAt(j - 1)) {
                    matrix[i][j] = matrix[i - 1][j - 1];
                } else {
                    matrix[i][j] = Math.min(
                        matrix[i - 1][j - 1] + 1,
                        matrix[i][j - 1] + 1,
                        matrix[i - 1][j] + 1
                    );
                }
            }
        }

        const maxLen = Math.max(len1, len2);
        return maxLen === 0 ? 1 : (maxLen - matrix[len2][len1]) / maxLen;
    }

    analyzeCallLatency(audioData, config) {
        console.log('⏱️ Analyzing call latency...');
        
        const totalDurationMinutes = audioData ? audioData.duration / 60 : 3.0; // Default 3 minutes
        const idealMin = config.idealCallDurationMin;
        const idealMax = config.idealCallDurationMax;
        
        const latencyAnalysis = {
            totalDurationMinutes,
            idealRangeMin: idealMin,
            idealRangeMax: idealMax,
            withinIdealRange: totalDurationMinutes >= idealMin && totalDurationMinutes <= idealMax,
            deviationFromIdeal: 0
        };
        
        if (totalDurationMinutes < idealMin) {
            latencyAnalysis.deviationFromIdeal = idealMin - totalDurationMinutes;
            latencyAnalysis.status = 'too_short';
        } else if (totalDurationMinutes > idealMax) {
            latencyAnalysis.deviationFromIdeal = totalDurationMinutes - idealMax;
            latencyAnalysis.status = 'too_long';
        } else {
            latencyAnalysis.status = 'optimal';
        }
        
        console.log(`✅ Call duration: ${totalDurationMinutes.toFixed(1)} minutes (${latencyAnalysis.status})`);
        return latencyAnalysis;
    }

    async detectIntents(transcript) {
        console.log('🎭 Detecting intents using custom logic...');

        const turns = this.parseTranscriptTurns(transcript);
        const intentMappings = [];
        const detectedIntents = new Set();
        let flowScore = 0;
        let currentFlowIndex = 0;

        // Process each turn with custom intent detection
        for (let i = 0; i < turns.length; i++) {
            const turn = turns[i];
            const { intent, step, confidence } = this.customIntentDetection(turn.text, turn.speaker, i, turns);

            intentMappings.push({
                turnNumber: i + 1,
                speaker: turn.speaker,
                text: turn.text,
                detectedIntent: intent,
                confidence,
                conversationStep: step
            });

            // Track detected intents for flow analysis
            if (step !== 'unknown') {
                detectedIntents.add(step);

                // Check if this intent follows the expected flow
                const expectedIndex = this.expectedFlow.indexOf(step);
                if (expectedIndex >= currentFlowIndex) {
                    flowScore += 10;
                    currentFlowIndex = expectedIndex;
                } else {
                    flowScore -= 5; // Penalty for out-of-order flow
                }
            }
        }

        // Calculate flow completeness bonus
        const completedSteps = detectedIntents.size;
        const totalRequiredSteps = this.expectedFlow.length;
        const completenessBonus = (completedSteps / totalRequiredSteps) * 20;
        flowScore += completenessBonus;

        console.log(`✅ Detected intents for ${intentMappings.length} turns`);
        console.log(`📊 Flow score: ${flowScore.toFixed(1)}/100`);
        console.log(`🎯 Completed conversation steps: ${completedSteps}/${totalRequiredSteps}`);

        return {
            intentMappings,
            flowScore: Math.max(0, Math.min(100, flowScore)),
            completedSteps,
            totalRequiredSteps,
            detectedIntents: Array.from(detectedIntents)
        };
    }

    customIntentDetection(text, speaker, turnIndex, allTurns) {
        const textLower = text.toLowerCase();
        let bestMatch = { intent: 'general_conversation', step: 'unknown', confidence: 0.3 };

        // Analyze each intent pattern
        for (const [intentType, intentData] of Object.entries(this.intentPatterns)) {
            let matchCount = 0;
            let totalPatterns = intentData.patterns.length;

            for (const pattern of intentData.patterns) {
                if (pattern.test(text)) {
                    matchCount++;
                }
            }

            const confidence = matchCount / totalPatterns;
            if (confidence > bestMatch.confidence) {
                bestMatch = {
                    intent: this.getIntentDescription(intentType, text),
                    step: intentType,
                    confidence: Math.min(0.95, confidence + 0.1) // Cap at 95% confidence
                };
            }
        }

        // Context-aware adjustments
        bestMatch = this.adjustIntentWithContext(bestMatch, speaker, turnIndex, allTurns, textLower);

        return bestMatch;
    }

    getIntentDescription(intentType, text) {
        const descriptions = {
            greeting: 'Opening greeting and welcome',
            problem_identification: 'Identifying customer issue or need',
            information_gathering: 'Collecting customer information',
            solution_offering: 'Providing solution or assistance',
            confirmation: 'Confirming understanding or next steps',
            closing: 'Closing conversation and farewell'
        };
        return descriptions[intentType] || 'General conversation';
    }

    adjustIntentWithContext(match, speaker, turnIndex, allTurns, textLower) {
        // Boost confidence for agent responses that follow expected patterns
        if (speaker.toLowerCase().includes('agent') || speaker.toLowerCase().includes('bot')) {
            // First turn should likely be greeting
            if (turnIndex === 0 && match.step === 'greeting') {
                match.confidence = Math.min(0.95, match.confidence + 0.2);
            }

            // Last few turns should likely be closing
            if (turnIndex >= allTurns.length - 3 && match.step === 'closing') {
                match.confidence = Math.min(0.95, match.confidence + 0.15);
            }
        }

        // Customer satisfaction analysis
        if (speaker.toLowerCase().includes('customer') || speaker.toLowerCase().includes('user')) {
            const satisfaction = this.analyzeSatisfaction(textLower);
            if (satisfaction !== 'neutral') {
                match.satisfaction = satisfaction;
                match.confidence = Math.min(0.95, match.confidence + 0.1);
            }
        }

        return match;
    }

    analyzeSatisfaction(textLower) {
        for (const pattern of this.satisfactionPatterns.positive) {
            if (pattern.test(textLower)) {
                return 'positive';
            }
        }

        for (const pattern of this.satisfactionPatterns.negative) {
            if (pattern.test(textLower)) {
                return 'negative';
            }
        }

        return 'neutral';
    }



    parseTranscriptTurns(transcript) {
        const turns = [];
        const lines = transcript.split('\n');

        lines.forEach(line => {
            line = line.trim();
            if (!line) return;

            let speaker, text;

            // Enhanced speaker detection patterns
            if (line.match(/^(Chat Bot|Bot|Agent|Support|Assistant):/i)) {
                speaker = 'agent';
                text = line.includes(':') ? line.split(':', 2)[1].trim() : line;
            } else if (line.match(/^(Human|User|Customer|Client|Caller):/i)) {
                speaker = 'customer';
                text = line.includes(':') ? line.split(':', 2)[1].trim() : line;
            } else if (line.includes(':')) {
                // Generic speaker:text format
                const parts = line.split(':', 2);
                speaker = parts[0].trim().toLowerCase().includes('bot') ||
                         parts[0].trim().toLowerCase().includes('agent') ? 'agent' : 'customer';
                text = parts[1].trim();
            } else {
                return; // Skip lines without clear speaker identification
            }

            if (text && text.length > 0) {
                turns.push({ speaker, text });
            }
        });

        return turns;
    }

    calculateWeightedScore(silenceSegments, repetitions, latencyAnalysis, intentFlow) {
        console.log('📊 Calculating weighted scores...');

        const weights = {
            silenceCompliance: 0.25,
            repetitionAvoidance: 0.20,
            latencyOptimization: 0.25,
            intentFlowAccuracy: 0.30
        };
        
        const scores = {};
        
        // Silence score
        const silenceViolations = silenceSegments.length;
        const maxExpectedViolations = 5;
        scores.silenceCompliance = Math.max(0, 100 - (silenceViolations / maxExpectedViolations) * 100);
        
        // Repetition score
        const repetitionCount = repetitions.length;
        const maxExpectedRepetitions = 3;
        scores.repetitionAvoidance = Math.max(0, 100 - (repetitionCount / maxExpectedRepetitions) * 100);
        
        // Latency score
        if (latencyAnalysis.withinIdealRange) {
            scores.latencyOptimization = 100;
        } else {
            const deviation = latencyAnalysis.deviationFromIdeal;
            const maxAcceptableDeviation = 2.0;
            scores.latencyOptimization = Math.max(0, 100 - (deviation / maxAcceptableDeviation) * 100);
        }
        
        // Intent flow score (using new structure)
        if (intentFlow && intentFlow.flowScore !== undefined) {
            scores.intentFlowAccuracy = intentFlow.flowScore;
        } else if (intentFlow && intentFlow.intentMappings && intentFlow.intentMappings.length > 0) {
            const avgConfidence = intentFlow.intentMappings.reduce((sum, intent) => sum + intent.confidence, 0) / intentFlow.intentMappings.length;
            scores.intentFlowAccuracy = avgConfidence * 100;
        } else {
            scores.intentFlowAccuracy = 50; // Default score when no intent data
        }
        
        const overallScore = Object.keys(weights).reduce((sum, metric) => {
            return sum + (scores[metric] * weights[metric]);
        }, 0);
        
        const scoreBreakdown = {
            overallScore,
            componentScores: scores,
            weights,
            explanations: {
                silenceCompliance: `Found ${silenceViolations} silence violations (threshold: ${maxExpectedViolations})`,
                repetitionAvoidance: `Found ${repetitionCount} repetitions (threshold: ${maxExpectedRepetitions})`,
                latencyOptimization: `Call duration: ${latencyAnalysis.totalDurationMinutes.toFixed(1)}min (ideal: ${latencyAnalysis.idealRangeMin}-${latencyAnalysis.idealRangeMax}min)`,
                intentFlowAccuracy: `Average intent confidence: ${intentFlow.length > 0 ? (intentFlow.reduce((sum, intent) => sum + intent.confidence, 0) / intentFlow.length * 100).toFixed(1) : 0}%`
            }
        };
        
        console.log(`✅ Overall score calculated: ${overallScore.toFixed(1)}/100`);
        return { overallScore, scoreBreakdown };
    }

    generateVisualizationData(audioData, silenceSegments) {
        console.log('📈 Generating visualization data...');
        
        const duration = audioData ? audioData.duration : 180; // Default 3 minutes
        
        // Generate mock waveform data for visualization
        const sampleRate = 100; // 100 samples per second for visualization
        const totalSamples = Math.floor(duration * sampleRate);
        const waveformData = [];
        
        for (let i = 0; i < totalSamples; i++) {
            const time = i / sampleRate;
            // Generate realistic waveform with some variation
            const amplitude = 0.5 * Math.sin(2 * Math.PI * 0.1 * time) * Math.exp(-time / 60) + 
                            0.3 * Math.random() - 0.15;
            waveformData.push({
                time,
                amplitude
            });
        }
        
        const visualizationData = {
            duration,
            waveformData,
            silenceMarkers: silenceSegments.map(segment => ({
                start: segment.startTime,
                end: segment.endTime,
                duration: segment.duration,
                speaker: segment.speaker
            })),
            sampleRate
        };
        
        console.log(`✅ Visualization data generated with ${silenceSegments.length} silence markers`);
        return visualizationData;
    }
}

module.exports = { CallQAAnalyzer };
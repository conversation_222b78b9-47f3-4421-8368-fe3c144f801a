{"ast": null, "code": "/**\n * @license lucide-react v0.294.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst PiggyBank = createLucideIcon(\"PiggyBank\", [[\"path\", {\n  d: \"M19 5c-1.5 0-2.8 1.4-3 2-3.5-1.5-11-.3-11 5 0 1.8 0 3 2 4.5V20h4v-2h3v2h4v-4c1-.5 1.7-1 2-2h2v-4h-2c0-1-.5-1.5-1-2h0V5z\",\n  key: \"uf6l00\"\n}], [\"path\", {\n  d: \"M2 9v1c0 1.1.9 2 2 2h1\",\n  key: \"nm575m\"\n}], [\"path\", {\n  d: \"M16 11h0\",\n  key: \"k2aug8\"\n}]]);\nexport { PiggyBank as default };", "map": {"version": 3, "names": ["PiggyBank", "createLucideIcon", "d", "key"], "sources": ["C:\\Users\\<USER>\\OneDrive\\Desktop\\MVP\\client\\node_modules\\lucide-react\\src\\icons\\piggy-bank.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name PiggyBank\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTkgNWMtMS41IDAtMi44IDEuNC0zIDItMy41LTEuNS0xMS0uMy0xMSA1IDAgMS44IDAgMyAyIDQuNVYyMGg0di0yaDN2Mmg0di00YzEtLjUgMS43LTEgMi0yaDJ2LTRoLTJjMC0xLS41LTEuNS0xLTJoMFY1eiIgLz4KICA8cGF0aCBkPSJNMiA5djFjMCAxLjEuOSAyIDIgMmgxIiAvPgogIDxwYXRoIGQ9Ik0xNiAxMWgwIiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/piggy-bank\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst PiggyBank = createLucideIcon('PiggyBank', [\n  [\n    'path',\n    {\n      d: 'M19 5c-1.5 0-2.8 1.4-3 2-3.5-1.5-11-.3-11 5 0 1.8 0 3 2 4.5V20h4v-2h3v2h4v-4c1-.5 1.7-1 2-2h2v-4h-2c0-1-.5-1.5-1-2h0V5z',\n      key: 'uf6l00',\n    },\n  ],\n  ['path', { d: 'M2 9v1c0 1.1.9 2 2 2h1', key: 'nm575m' }],\n  ['path', { d: 'M16 11h0', key: 'k2aug8' }],\n]);\n\nexport default PiggyBank;\n"], "mappings": ";;;;;;;;AAaM,MAAAA,SAAA,GAAYC,gBAAA,CAAiB,WAAa,GAC9C,CACE,QACA;EACEC,CAAG;EACHC,GAAK;AACP,EACF,EACA,CAAC,MAAQ;EAAED,CAAA,EAAG,wBAA0B;EAAAC,GAAA,EAAK;AAAA,CAAU,GACvD,CAAC,MAAQ;EAAED,CAAA,EAAG,UAAY;EAAAC,GAAA,EAAK;AAAA,CAAU,EAC1C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
Y# Voice Bot QA Analysis System

A comprehensive system for analyzing voice bot call quality, available in two implementations:

1. **Node.js/React Web Application** (Main System) - Production-ready web dashboard
2. **Google Colab Notebook** (Research/Testing) - Interactive notebook for analysis

This system provides silence detection, repetition analysis, conversational flow assessment, and visual waveform representation.

## Features

- **Call Score Analysis**: Overall QA scoring based on multiple metrics
- **Silence Detection**: Identifies silence violations longer than configurable thresholds
- **Repetition Analysis**: Detects repetitive bot responses using similarity algorithms
- **Latency Analysis**: Evaluates call duration against ideal ranges
- **Conversational Flow**: AI-powered intent detection and conversation step mapping
- **Visual Waveform**: Interactive audio waveform with silence markers
- **Responsive Dashboard**: Clean, mobile-friendly interface
- **File Upload Support**: Handles MP3, M4A, MP4, WAV, WebM, OGG audio files
- **Real-time Analysis**: Live processing with progress indicators

## Available Implementations

### 1. Web Application (Node.js + React)
**Location**: Main project files
**Use Case**: Production deployment, team collaboration, web-based analysis

**Technology Stack:**
- **Backend**: Node.js with Express.js, OpenAI API, FFmpeg, Multer
- **Frontend**: React 18, Recharts, React Dropzone, Lucide React
- **Features**: File uploads, real-time analysis, responsive dashboard

### 2. Google Colab Notebook
**Location**: `COLAB_NO_DEEPGRAM.py`
**Use Case**: Research, experimentation, quick analysis
**Documentation**: See `COLAB_DOCUMENTATION.md`

**Technology Stack:**
- **Python**: librosa, pydub, matplotlib, OpenAI API
- **Features**: Interactive analysis, visualization, URL/file processing

## Quick Start

Choose your preferred implementation:

### Option 1: Web Application (Recommended)

**Prerequisites:**
- Node.js 16+ and npm
- OpenAI API key

**Installation:**

1. **Clone and setup the project:**
```bash
git clone <repository-url>
cd voice-bot-qa-system
npm install
```

2. **Install backend dependencies:**
```bash
cd server
npm install
```

3. **Install frontend dependencies:**
```bash
cd ../client
npm install
```

4. **Configure environment:**
```bash
cd ../server
# Edit .env file with your OpenAI API key
```

### Running the Application

**Development mode (both frontend and backend):**
```bash
# From project root
npm run dev
```

**Or run separately:**
```bash
# Terminal 1 - Backend
cd server
npm run dev

# Terminal 2 - Frontend  
cd client
npm start
```

**Production mode:**
```bash
npm run build
npm start
```

The application will be available at:
- Frontend: http://localhost:3000
- Backend API: http://localhost:5000

### Option 2: Google Colab Notebook

1. **Open the notebook:**
   - Upload `COLAB_NO_DEEPGRAM.py` to Google Colab
   - Or copy the code directly into a new Colab notebook

2. **Set your OpenAI API key:**
   ```python
   OPENAI_API_KEY = "your-api-key-here"
   ```

3. **Run the analysis:**
   ```python
   # Initialize analyzer
   analyzer = CallQAAnalyzer(OPENAI_API_KEY)
   
   # Analyze with your data
   results = analyzer.analyze_call(audio_url, transcript, config)
   ```

**See `COLAB_DOCUMENTATION.md` for detailed usage instructions.**

## Usage

### 1. Upload Audio & Transcript
- Drag & drop audio files (MP3, M4A, MP4, WAV, WebM, OGG)
- Paste call transcript in the specified format:
  ```
  Chat Bot: Hello, this is...
  Human: Hi there...
  Chat Bot: How can I help you today?
  ```

### 2. Configure Analysis Parameters
- **Silence Threshold**: Minimum silence duration to flag (default: 5.0s)
- **Ideal Call Duration**: Expected call length range (default: 2-4 minutes)
- **Repetition Similarity**: Threshold for detecting similar responses (default: 80%)

### 3. View Results
- **Overall Score**: Weighted QA score out of 100
- **Metrics Dashboard**: Key performance indicators
- **Waveform Visualization**: Audio analysis with silence markers
- **Intent Flow**: Conversation step analysis
- **Detailed Reports**: Comprehensive breakdown of findings

## API Endpoints

### `GET /api/health`
Health check endpoint

### `POST /api/analyze`
Main analysis endpoint
- **Body**: FormData with `audioFile`, `transcript`, and `config`
- **Response**: Complete analysis results

### `GET /api/test-sample`
Test endpoint with sample data

## Configuration

### Analysis Parameters
```javascript
{
  silenceThreshold: 5.0,              // seconds
  idealCallDurationMin: 2.0,          // minutes  
  idealCallDurationMax: 4.0,          // minutes
  repetitionSimilarityThreshold: 0.8  // 0.0-1.0
}
```

### Scoring Weights
- Silence Compliance: 25%
- Repetition Avoidance: 25%  
- Latency Optimization: 25%
- Intent Flow Accuracy: 25%

## Architecture

```
voice-bot-qa-system/
├── server/                 # Node.js backend
│   ├── services/          # Core analysis services
│   │   ├── analyzer.js    # Main QA analysis logic
│   │   └── audioProcessor.js # Audio processing utilities
│   ├── uploads/           # Temporary file storage
│   └── index.js          # Express server
├── client/                # React frontend
│   ├── src/
│   │   ├── components/    # React components
│   │   │   ├── Dashboard.js
│   │   │   ├── AnalysisForm.js
│   │   │   ├── ScoreOverview.js
│   │   │   ├── WaveformChart.js
│   │   │   ├── MetricsGrid.js
│   │   │   ├── IntentFlow.js
│   │   │   └── DetailedResults.js
│   │   └── App.js        # Main application
└── package.json          # Root package configuration
```

## Analysis Components

### 1. Silence Detection
- Processes audio files to identify silence periods
- Flags violations exceeding configured threshold
- Associates silence with speaker turns

### 2. Repetition Analysis
- Compares bot responses using similarity algorithms
- Identifies repetitive or scripted behavior
- Calculates similarity scores between turns

### 3. Intent Detection
- Uses OpenAI API for conversation analysis
- Maps turns to conversation steps
- Provides confidence scores for each intent

### 4. Latency Analysis
- Evaluates call duration against ideal ranges
- Identifies calls that are too short or too long
- Calculates deviation from optimal duration

## Customization

### Adding New Analysis Metrics
1. Extend the analyzer service in `server/services/analyzer.js`
2. Update scoring weights in `calculateWeightedScore()`
3. Add UI components in the React frontend

### Modifying Conversation Steps
Update the `conversationSteps` object in the analyzer to match your specific use case:

```javascript
const conversationSteps = {
  greeting: 'Initial greeting and identification',
  // Add your custom steps here
};
```

## Troubleshooting

### Common Issues

**Audio processing fails:**
- Ensure FFmpeg is properly installed
- Check file format compatibility
- Verify file size limits (100MB max)

**OpenAI API errors:**
- Verify API key is correctly set
- Check API quota and billing
- Ensure network connectivity

**File upload issues:**
- Check file permissions in uploads directory
- Verify multer configuration
- Ensure adequate disk space

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## License

MIT License - see LICENSE file for details

## File Structure

```
voice-bot-qa-system/
├── README.md                           # Main documentation
├── COLAB_DOCUMENTATION.md              # Colab notebook documentation
├── COLAB_NO_DEEPGRAM.py               # Google Colab implementation
├── package.json                       # Root package configuration
├── install.bat                        # Windows installation script
├── start.bat                          # Windows startup script
├── server/                            # Node.js backend
│   ├── package.json
│   ├── .env                          # Environment configuration
│   ├── index.js                      # Express server
│   └── services/                     # Core services
│       ├── analyzer.js               # Main analysis logic
│       └── audioProcessor.js         # Audio processing
└── client/                           # React frontend
    ├── package.json
    ├── public/
    │   └── index.html
    └── src/
        ├── App.js                    # Main application
        ├── index.js                  # React entry point
        ├── index.css                 # Global styles
        └── components/               # React components
            ├── AnalysisForm.js       # File upload and configuration
            ├── Dashboard.js          # Main dashboard
            ├── ScoreOverview.js      # QA score display
            ├── MetricsGrid.js        # Key metrics
            ├── WaveformChart.js      # Audio visualization
            ├── IntentFlow.js         # Conversation analysis
            └── DetailedResults.js    # Comprehensive results
```

## Implementation Comparison

| Feature | Web Application | Colab Notebook |
|---------|----------------|----------------|
| **Deployment** | Production-ready | Research/Testing |
| **User Interface** | Modern web dashboard | Interactive notebook |
| **File Upload** | Drag & drop, multiple formats | URL or file path |
| **Real-time Analysis** | ✅ Progress indicators | ✅ Step-by-step execution |
| **Visualization** | Interactive charts | Matplotlib plots |
| **Collaboration** | Multi-user web access | Shared notebook |
| **Customization** | Component-based | Code modification |
| **Audio Processing** | FFmpeg (production) | librosa/pydub (research) |
| **Scalability** | High (web server) | Limited (single session) |

## Support

For issues and questions:
1. **Web Application**: Check the troubleshooting section in this README
2. **Colab Notebook**: See `COLAB_DOCUMENTATION.md` for specific guidance
3. **General Issues**: Create an issue in the reposi
{"ast": null, "code": "/**\n * @license lucide-react v0.294.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst GitGraph = createLucideIcon(\"GitGraph\", [[\"circle\", {\n  cx: \"5\",\n  cy: \"6\",\n  r: \"3\",\n  key: \"1qnov2\"\n}], [\"path\", {\n  d: \"M5 9v6\",\n  key: \"158jrl\"\n}], [\"circle\", {\n  cx: \"5\",\n  cy: \"18\",\n  r: \"3\",\n  key: \"104gr9\"\n}], [\"path\", {\n  d: \"M12 3v18\",\n  key: \"108xh3\"\n}], [\"circle\", {\n  cx: \"19\",\n  cy: \"6\",\n  r: \"3\",\n  key: \"108a5v\"\n}], [\"path\", {\n  d: \"M16 15.7A9 9 0 0 0 19 9\",\n  key: \"1e3vqb\"\n}]]);\nexport { GitGraph as default };", "map": {"version": 3, "names": ["GitGraph", "createLucideIcon", "cx", "cy", "r", "key", "d"], "sources": ["C:\\Users\\<USER>\\OneDrive\\Desktop\\MVP\\client\\node_modules\\lucide-react\\src\\icons\\git-graph.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name GitGraph\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8Y2lyY2xlIGN4PSI1IiBjeT0iNiIgcj0iMyIgLz4KICA8cGF0aCBkPSJNNSA5djYiIC8+CiAgPGNpcmNsZSBjeD0iNSIgY3k9IjE4IiByPSIzIiAvPgogIDxwYXRoIGQ9Ik0xMiAzdjE4IiAvPgogIDxjaXJjbGUgY3g9IjE5IiBjeT0iNiIgcj0iMyIgLz4KICA8cGF0aCBkPSJNMTYgMTUuN0E5IDkgMCAwIDAgMTkgOSIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/git-graph\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst GitGraph = createLucideIcon('GitGraph', [\n  ['circle', { cx: '5', cy: '6', r: '3', key: '1qnov2' }],\n  ['path', { d: 'M5 9v6', key: '158jrl' }],\n  ['circle', { cx: '5', cy: '18', r: '3', key: '104gr9' }],\n  ['path', { d: 'M12 3v18', key: '108xh3' }],\n  ['circle', { cx: '19', cy: '6', r: '3', key: '108a5v' }],\n  ['path', { d: 'M16 15.7A9 9 0 0 0 19 9', key: '1e3vqb' }],\n]);\n\nexport default GitGraph;\n"], "mappings": ";;;;;;;;AAaM,MAAAA,QAAA,GAAWC,gBAAA,CAAiB,UAAY,GAC5C,CAAC,QAAU;EAAEC,EAAI;EAAKC,EAAI;EAAKC,CAAG;EAAKC,GAAK;AAAA,CAAU,GACtD,CAAC,MAAQ;EAAEC,CAAA,EAAG,QAAU;EAAAD,GAAA,EAAK;AAAA,CAAU,GACvC,CAAC,QAAU;EAAEH,EAAI;EAAKC,EAAI;EAAMC,CAAG;EAAKC,GAAK;AAAA,CAAU,GACvD,CAAC,MAAQ;EAAEC,CAAA,EAAG,UAAY;EAAAD,GAAA,EAAK;AAAA,CAAU,GACzC,CAAC,QAAU;EAAEH,EAAI;EAAMC,EAAI;EAAKC,CAAG;EAAKC,GAAK;AAAA,CAAU,GACvD,CAAC,MAAQ;EAAEC,CAAA,EAAG,yBAA2B;EAAAD,GAAA,EAAK;AAAA,CAAU,EACzD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}